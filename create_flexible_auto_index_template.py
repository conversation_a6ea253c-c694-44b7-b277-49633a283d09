import openpyxl
from copy import copy

# 创建一个演示 {{auto_index}} 灵活性的模板
print("=== 创建灵活的 {{auto_index}} 演示模板 ===")

# 创建新的工作簿
wb = openpyxl.Workbook()
ws = wb.active

# 设置标题
ws.merge_cells('A1:F2')
title_cell = ws.cell(row=1, column=1)
title_cell.value = "{{auto_index}} 占位符演示模板"
title_cell.font = openpyxl.styles.Font(name='宋体', size=16, bold=True)
title_cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')

# 设置客户信息行
ws.merge_cells('A3:B3')
customer_label = ws.cell(row=3, column=1)
customer_label.value = "客户名称："
customer_label.font = openpyxl.styles.Font(name='宋体', size=11)

ws.merge_cells('C3:D3')
customer_value = ws.cell(row=3, column=3)
customer_value.value = "{{name}}"
customer_value.font = openpyxl.styles.Font(name='宋体', size=11, bold=True, color='FFFF0000')  # 红色

ws.merge_cells('E3:F3')
date_label = ws.cell(row=3, column=5)
date_label.value = "日期："
date_label.font = openpyxl.styles.Font(name='宋体', size=11)

# 设置表头
headers = ["序号", "编号", "商品名称", "规格", "单位", "备注"]
for col, header in enumerate(headers, 1):
    cell = ws.cell(row=4, column=col)
    cell.value = header
    cell.font = openpyxl.styles.Font(name='宋体', size=11, bold=True)
    cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
    cell.border = openpyxl.styles.Border(
        left=openpyxl.styles.Side(style='thin'),
        right=openpyxl.styles.Side(style='thin'),
        top=openpyxl.styles.Side(style='thin'),
        bottom=openpyxl.styles.Side(style='thin')
    )

# 设置模板行 - 演示 {{auto_index}} 在不同位置
template_data = [
    "{{auto_index}}",      # 第1列：序号
    "{{list.code}}",       # 第2列：编号
    "{{list.name}}",       # 第3列：商品名称
    "{{list.spec}}",       # 第4列：规格
    "{{list.unit}}",       # 第5列：单位
    "第{{auto_index}}项"    # 第6列：备注中也使用序号
]

for col, value in enumerate(template_data, 1):
    cell = ws.cell(row=5, column=col)
    cell.value = value
    cell.font = openpyxl.styles.Font(name='宋体', size=11)
    cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
    cell.border = openpyxl.styles.Border(
        left=openpyxl.styles.Side(style='thin'),
        right=openpyxl.styles.Side(style='thin'),
        top=openpyxl.styles.Side(style='thin'),
        bottom=openpyxl.styles.Side(style='thin')
    )

# 设置合计行
ws.merge_cells('A6:E6')
total_label = ws.cell(row=6, column=1)
total_label.value = "合计："
total_label.font = openpyxl.styles.Font(name='宋体', size=11, bold=True)
total_label.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')

# 设置制单人行
creator_label = ws.cell(row=7, column=1)
creator_label.value = "制单人："
creator_label.font = openpyxl.styles.Font(name='宋体', size=11)

creator_value = ws.cell(row=7, column=2)
creator_value.value = "{{creator}}"
creator_value.font = openpyxl.styles.Font(name='宋体', size=11, bold=True, color='FFFF0000')  # 红色

# 设置列宽
column_widths = [8, 12, 20, 15, 8, 15]  # 序号, 编号, 商品名称, 规格, 单位, 备注
for col, width in enumerate(column_widths, 1):
    ws.column_dimensions[chr(64 + col)].width = width

# 保存模板
template_path = 'templates/flexible_auto_index.xlsx'
wb.save(template_path)
print(f"✅ 灵活演示模板已保存: {template_path}")

print("\n=== 模板特点 ===")
print("1. 第1列: {{auto_index}} - 标准序号位置")
print("2. 第6列: 第{{auto_index}}项 - 在文本中使用序号")
print("3. 演示了 {{auto_index}} 可以在任意位置使用")
print("4. 演示了 {{auto_index}} 可以与其他文本组合")

print("\n=== 模板结构 ===")
print("序号 | 编号          | 商品名称      | 规格          | 单位          | 备注")
print("{{auto_index}} | {{list.code}} | {{list.name}} | {{list.spec}} | {{list.unit}} | 第{{auto_index}}项")

print("\n=== 使用示例数据格式 ===")
print("""
{
  "template_name": "flexible_auto_index",
  "data": {
    "list": [
      {"code": "P001", "name": "产品A", "spec": "规格A", "unit": "个"},
      {"code": "P002", "name": "产品B", "spec": "规格B", "unit": "套"}
    ],
    "name": "演示客户",
    "creator": "测试员"
  }
}
""")

print("\n=== 预期结果 ===")
print("序号 | 编号 | 商品名称 | 规格   | 单位 | 备注")
print(" 1   | P001 | 产品A   | 规格A  | 个   | 第1项")
print(" 2   | P002 | 产品B   | 规格B  | 套   | 第2项")

print("\n=== 创建完成 ===")
