import requests
import json

# 通过API测试Word模板
data = {
    "data": {
        "cdkh": "API_TEST_001",
        "list": [
            {"name": "API测试产品A", "quantity": 15, "note": "API测试备注A"},
            {"name": "API测试产品B", "quantity": 25, "note": "API测试备注B"},
            {"name": "API测试产品C", "quantity": 10, "note": "API测试备注C"}
        ],
        "creator": "API测试员",
        "date": "2025-06-20",
        "deliveryman": "API送货员"
    },
    "template_name": "委外加工过程不良品记录表"
}

print("📄 通过API测试Word模板")
print("=" * 50)

try:
    print("📤 发送API请求...")
    print(f"URL: http://localhost:5000/api/print")
    print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 发送请求
    response = requests.post(
        "http://localhost:5000/api/print", 
        json=data, 
        timeout=60,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"\n📥 API响应:")
    print(f"状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    
    if response.status_code == 200:
        print("✅ API调用成功")
        try:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        except:
            print(f"响应内容(文本): {response.text}")
        
        print("\n🔗 查看链接:")
        print("   📄 普通页面: http://localhost:5000/display")
        print("   🖨️ 打印页面: http://localhost:5000/print")
        
        print("\n🎯 预期功能:")
        print("✅ 基本占位符: {{cdkh}} -> API_TEST_001")
        print("✅ 动态列表: 3行数据")
        print("✅ 自增序号: 1, 2, 3")
        print("✅ 普通字段: creator, date等")
        
    else:
        print(f"❌ API调用失败")
        print(f"错误内容: {response.text}")
        
        if response.status_code == 503:
            print("\n🔍 503错误分析:")
            print("- 服务器内部错误")
            print("- 可能是Word处理过程中的异常")
        elif response.status_code == 400:
            print("\n🔍 400错误分析:")
            print("- 请求数据格式错误")
            print("- 缺少必要字段")
        elif response.status_code == 404:
            print("\n🔍 404错误分析:")
            print("- API路径不存在")
            print("- 模板文件未找到")
        
except requests.exceptions.Timeout:
    print("⏰ 请求超时")
    print("Word处理可能需要更长时间")
except requests.exceptions.ConnectionError:
    print("🔌 连接错误")
    print("服务器可能未启动或端口被占用")
except Exception as e:
    print(f"❌ 请求异常: {e}")

print("\n" + "=" * 50)
print("📄 API Word测试完成")

# 额外信息
print("\n💡 调试提示:")
print("1. 直接函数调用成功，说明Word处理逻辑正确")
print("2. 如果API调用失败，可能是Flask路由或错误处理问题")
print("3. 检查服务器日志获取详细错误信息")
print("4. 确认服务器正在运行且监听正确端口")
