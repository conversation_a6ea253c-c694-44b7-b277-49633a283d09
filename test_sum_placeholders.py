import requests

# 测试 {{sum.field}} 占位符功能
data = {
    "data": {
        "list": [
            {"batno": "SUM001", "name": "SUM占位符测试1", "matrial": "材料1", "unit": "个", "qty": 10, "price": 100, "amount": 1000, "remark": "备注1"},
            {"batno": "SUM002", "name": "SUM占位符测试2", "matrial": "材料2", "unit": "套", "qty": 25, "price": 200, "amount": 5000, "remark": "备注2"},
            {"batno": "SUM003", "name": "SUM占位符测试3", "matrial": "材料3", "unit": "件", "qty": 15, "price": 150, "amount": 2250, "remark": "备注3"},
            {"batno": "SUM004", "name": "SUM占位符测试4", "matrial": "材料4", "unit": "台", "qty": 8, "price": 500, "amount": 4000, "remark": "备注4"}
        ],
        "name": "SUM占位符测试客户",
        "creator": "占位符测试员"
    },
    "template_name": "delivery_with_sum_placeholders"  # 使用包含SUM占位符的模板
}

print("🧮 测试 {{sum.field}} 占位符功能")
print("=" * 60)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 测试内容:")
    print("- 模板: delivery_with_sum_placeholders.xlsx")
    print("- SUM占位符: {{sum.qty}}, {{sum.amount}}")
    print("- 数据: 4行测试数据")
    
    print("\n🎯 SUM占位符特点:")
    print("✅ 智能计算: 自动计算指定字段的总和")
    print("✅ 灵活位置: 可以放在任意位置")
    print("✅ 动态长度: 自动适应list长度变化")
    print("✅ 数据类型: 支持数字和字符串数字")
    print("✅ 错误处理: 自动跳过非数值数据")
    
    print("\n📊 计算验证:")
    print("数量求和: 10 + 25 + 15 + 8 = 58")
    print("金额求和: 1000 + 5000 + 2250 + 4000 = 12250")
    
    print("\n🎨 预期显示效果:")
    print("序号 | 批号   | 名称           | 材质  | 单位 | 数量 | 单价 | 金额 | 备注")
    print("-" * 75)
    print(" 1   | SUM001 | SUM占位符测试1 | 材料1 | 个   | 10   | 100  | 1000 | 备注1")
    print(" 2   | SUM002 | SUM占位符测试2 | 材料2 | 套   | 25   | 200  | 5000 | 备注2")
    print(" 3   | SUM003 | SUM占位符测试3 | 材料3 | 件   | 15   | 150  | 2250 | 备注3")
    print(" 4   | SUM004 | SUM占位符测试4 | 材料4 | 台   | 8    | 500  | 4000 | 备注4")
    print("合计：                                        | 58   |      | 12250|")
    
    print("\n💡 {{sum.field}} vs =SUM() 对比:")
    print("{{sum.field}} 占位符:")
    print("  ✅ 动态长度: 自动适应list长度")
    print("  ✅ 简单易用: 无需手动设置范围")
    print("  ✅ 智能计算: 自动处理数据类型")
    print("  ✅ 错误容忍: 跳过无效数据")
    
    print("\n=SUM() 公式:")
    print("  ✅ Excel原生: 支持复杂计算")
    print("  ❌ 固定范围: 需要手动调整范围")
    print("  ❌ 复杂设置: 需要预设公式")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
    print("\n🚀 使用场景:")
    print("1. 动态长度列表的自动求和")
    print("2. 简单的数值统计")
    print("3. 不需要复杂公式的场景")
    print("4. 快速模板开发")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 60)
print("🧮 {{sum.field}} 占位符测试完成！")
print("这是一个革命性的功能，解决了动态长度列表的求和难题！")
