from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "List格式模板"

# 定义样式
title_font = Font(name='宋体', size=16, bold=True)
header_font = Font(name='宋体', size=12, bold=True, color='FFFFFF')
normal_font = Font(name='宋体', size=10)

# 定义填充色
header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

# 定义对齐方式
center_align = Alignment(horizontal='center', vertical='center')
left_align = Alignment(horizontal='left', vertical='center')

# 定义边框
thin_border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 创建标题
ws.merge_cells('A1:G1')
ws['A1'] = "发货单 - List格式"
ws['A1'].font = title_font
ws['A1'].alignment = center_align
ws.row_dimensions[1].height = 35

# 基本信息区域
ws['A3'] = "客户名称:"
ws['A3'].font = Font(name='宋体', size=11, bold=True)
ws['B3'] = "{{name}}"
ws['B3'].font = normal_font

ws['E3'] = "制单人:"
ws['E3'].font = Font(name='宋体', size=11, bold=True)
ws['F3'] = "{{creator}}"
ws['F3'].font = normal_font

ws['A4'] = "发货日期:"
ws['A4'].font = Font(name='宋体', size=11, bold=True)
ws['B4'] = "{{delivery_date}}"
ws['B4'].font = normal_font

# 商品清单标题
ws['A6'] = "商品清单:"
ws['A6'].font = Font(name='宋体', size=12, bold=True)

# 动态列表标记
ws['A7'] = "{{list:list}}"
ws['A7'].font = Font(name='宋体', size=9, color='666666')

# 列表表头
headers = ['批次号', '名称', '材质', '单位', '数量', '备注', '发货标志']
for i, header in enumerate(headers):
    cell = ws.cell(row=8, column=i+1, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = center_align
    cell.border = thin_border

# 列表模板行（第9行）- 使用 {{list.field}} 格式
ws['A9'] = "{{list.batno}}"      # 批次号
ws['B9'] = "{{list.name}}"       # 名称
ws['C9'] = "{{list.matrial}}"    # 材质
ws['D9'] = "{{list.unit}}"       # 单位
ws['E9'] = "{{list.quantity}}"   # 数量
ws['F9'] = "{{list.note}}"       # 备注
ws['G9'] = "{{list.delivery_f}}" # 发货标志

# 设置模板行样式
for col in range(1, 8):
    cell = ws.cell(row=9, column=col)
    cell.font = normal_font
    cell.alignment = left_align if col in [1, 2, 3, 6] else center_align
    cell.border = thin_border

# 列表后的内容（这些会在动态插入列表后自动向下移动）
ws['A11'] = "发货说明:"
ws['A11'].font = Font(name='宋体', size=11, bold=True)

ws.merge_cells('A12:G12')
ws['A12'] = "{{delivery_note}}"
ws['A12'].font = normal_font
ws['A12'].alignment = left_align

ws['A14'] = "发货人签名: ________________"
ws['A14'].font = normal_font

ws['E14'] = "收货人签名: ________________"
ws['E14'].font = normal_font

ws['A16'] = "发货时间: ________________"
ws['A16'].font = normal_font

ws['E16'] = "收货时间: ________________"
ws['E16'].font = normal_font

# 设置列宽
column_widths = {
    'A': 12,  # 批次号
    'B': 20,  # 名称
    'C': 12,  # 材质
    'D': 8,   # 单位
    'E': 8,   # 数量
    'F': 15,  # 备注
    'G': 10   # 发货标志
}

for col, width in column_widths.items():
    ws.column_dimensions[col].width = width

# 设置行高
ws.row_dimensions[8].height = 25  # 表头行
ws.row_dimensions[9].height = 22  # 模板行
for row in range(11, 17):
    ws.row_dimensions[row].height = 20

# 保存工作簿
wb.save("templates/list_format_delivery.xlsx")
print("List格式发货单模板已创建: templates/list_format_delivery.xlsx")
print("\n模板说明:")
print("1. {{list:list}} - 动态列表标记")
print("2. {{list.字段名}} - 使用list.字段名格式的占位符")
print("3. 支持您提供的数据格式")
print("4. 与 {{item.字段名}} 格式兼容")
