from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "合并单元格模板"

# 设置单元格样式
header_font = Font(name='宋体', size=14, bold=True)
normal_font = Font(name='宋体', size=11)
border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 创建标题（合并单元格）
ws.merge_cells('A1:F1')
ws['A1'] = "销售订单"
ws['A1'].font = Font(name='宋体', size=16, bold=True)
ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

# 添加订单信息（使用合并单元格）
ws.merge_cells('A3:B3')
ws['A3'] = "订单号: {{order_id}}"
ws['A3'].font = normal_font

ws.merge_cells('D3:F3')
ws['D3'] = "订单日期: {{order_date}}"
ws['D3'].font = normal_font

ws.merge_cells('A4:B4')
ws['A4'] = "客户名称: {{customer_name}}"
ws['A4'].font = normal_font

ws.merge_cells('D4:F4')
ws['D4'] = "联系电话: {{phone}}"
ws['D4'].font = normal_font

# 商品清单表头
ws['A6'] = "序号"
ws['B6'] = "商品名称"
ws['C6'] = "规格型号"
ws['D6'] = "数量"
ws['E6'] = "单价"
ws['F6'] = "金额"

# 设置表头样式和边框
for col in ['A6', 'B6', 'C6', 'D6', 'E6', 'F6']:
    ws[col].font = header_font
    ws[col].alignment = Alignment(horizontal='center')
    ws[col].border = border

# 添加商品数据行
for row in range(7, 10):
    ws[f'A{row}'] = row - 6  # 序号
    ws[f'B{row}'] = f"{{{{items{row-6}_name}}}}"
    ws[f'C{row}'] = f"{{{{items{row-6}_spec}}}}"
    ws[f'D{row}'] = f"{{{{items{row-6}_quantity}}}}"
    ws[f'E{row}'] = f"{{{{items{row-6}_price}}}}"
    ws[f'F{row}'] = f"{{{{items{row-6}_total}}}}"
    
    # 设置边框
    for col in ['A', 'B', 'C', 'D', 'E', 'F']:
        ws[f'{col}{row}'].border = border
        ws[f'{col}{row}'].font = normal_font

# 合计行（合并单元格）
ws.merge_cells('A11:E11')
ws['A11'] = "合计金额"
ws['A11'].font = header_font
ws['A11'].alignment = Alignment(horizontal='right')
ws['A11'].border = border

ws['F11'] = "{{total_amount}}"
ws['F11'].font = header_font
ws['F11'].border = border

# 备注（合并单元格）
ws.merge_cells('A13:F13')
ws['A13'] = "备注: {{remarks}}"
ws['A13'].font = normal_font

# 签名区域（合并单元格）
ws.merge_cells('A15:C15')
ws['A15'] = "制单人: {{creator}}"
ws['A15'].font = normal_font

ws.merge_cells('D15:F15')
ws['D15'] = "审核人: {{reviewer}}"
ws['D15'].font = normal_font

# 调整列宽
column_widths = {
    'A': 8,
    'B': 20,
    'C': 15,
    'D': 10,
    'E': 12,
    'F': 12
}

for col, width in column_widths.items():
    ws.column_dimensions[col].width = width

# 调整行高
ws.row_dimensions[1].height = 30  # 标题行
for row in range(3, 16):
    ws.row_dimensions[row].height = 20

# 保存工作簿
wb.save("templates/merged_cells_template.xlsx")
print("合并单元格模板已创建: templates/merged_cells_template.xlsx")
