import requests
import time

# 发送简单的两条数据测试
data = {
    "data": {
        "list": [
            {
                "batno": "ITEM1",
                "name": "第一个商品",
                "matrial": "材料1",
                "unit": "个"
            },
            {
                "batno": "ITEM2", 
                "name": "第二个商品",
                "matrial": "材料2",
                "unit": "件"
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 发送简单的两条数据测试 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("✅ API调用成功")
    print("请查看浏览器中的结果")
else:
    print("❌ API调用失败")

# 等待一下让用户查看结果
time.sleep(2)
