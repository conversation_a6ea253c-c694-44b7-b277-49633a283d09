import openpyxl
from openpyxl.utils import get_column_letter

# 检查模板结构
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查模板结构 ===")
print(f"最大行数: {ws.max_row}")
print(f"最大列数: {ws.max_column}")

print("\n=== 合并单元格信息 ===")
merged_ranges = list(ws.merged_cells.ranges)
print(f"合并单元格数量: {len(merged_ranges)}")
for i, merged_range in enumerate(merged_ranges):
    print(f"合并区域{i+1}: {merged_range}")

print("\n=== 详细单元格内容 ===")
for row in range(1, min(10, ws.max_row + 1)):
    print(f"\n第{row}行:")
    for col in range(1, min(10, ws.max_column + 1)):
        cell = ws.cell(row=row, column=col)
        col_letter = get_column_letter(col)
        value = cell.value if cell.value else ''
        
        # 检查是否在合并单元格中
        is_merged = False
        for merged_range in merged_ranges:
            if cell.coordinate in merged_range:
                is_merged = True
                break
        
        merged_info = " [合并]" if is_merged else ""
        print(f"  {col_letter}{row}: '{value}'{merged_info}")

print("\n=== 查找占位符 ===")
placeholders = []
for row in range(1, ws.max_row + 1):
    for col in range(1, ws.max_column + 1):
        cell = ws.cell(row=row, column=col)
        if cell.value and isinstance(cell.value, str) and '{{' in cell.value:
            placeholders.append({
                'coordinate': cell.coordinate,
                'row': row,
                'col': col,
                'value': cell.value
            })

print(f"找到{len(placeholders)}个占位符:")
for placeholder in placeholders:
    print(f"  {placeholder['coordinate']}: {placeholder['value']}")

print("\n=== 分析问题 ===")
# 检查第5行（包含{{list.field}}的行）是否有合并单元格
row_5_merged = []
for merged_range in merged_ranges:
    if any(ws.cell(row=5, column=col).coordinate in merged_range for col in range(1, ws.max_column + 1)):
        row_5_merged.append(merged_range)

if row_5_merged:
    print("⚠️ 第5行存在合并单元格，这可能导致动态列表插入问题:")
    for merged_range in row_5_merged:
        print(f"  合并区域: {merged_range}")
else:
    print("✅ 第5行没有合并单元格")

print("\n=== 建议 ===")
if merged_ranges:
    print("模板包含合并单元格，建议:")
    print("1. 重新创建模板，避免在动态列表区域使用合并单元格")
    print("2. 或者修改动态列表处理逻辑以正确处理合并单元格")
else:
    print("模板结构正常，问题可能在其他地方")
