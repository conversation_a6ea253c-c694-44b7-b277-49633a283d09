import requests
import openpyxl
import glob
import os

# 发送测试数据，重点检查红色字体
data = {
    "data": {
        "list": [
            {"batno": "RED001", "name": "红色测试1", "matrial": "材料A", "unit": "个"}
        ],
        "name": "红色客户名称",  # 这个应该是红色的
        "creator": "红色创建者"  # 这个也应该是红色的
    },
    "template_name": "delivery"
}

print("=== 测试红色字体保持 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 找到最新生成的Excel文件
    excel_files = glob.glob("filled_outputs/filled_delivery.xlsx_*.xlsx")
    if excel_files:
        latest_file = max(excel_files, key=os.path.getctime)
        print(f"检查最新文件: {latest_file}")

        # 加载Excel文件
        wb = openpyxl.load_workbook(latest_file)
        ws = wb.active

        print("\n=== 检查红色字体单元格 ===")

        # 检查应该是红色的单元格
        red_cells_to_check = [
            (3, 3, "客户名称"),  # {{name}} 应该是红色
            (7, 2, "创建者")    # {{creator}} 应该是红色 (合计行)
        ]

        for row_num, col_num, desc in red_cells_to_check:
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value else "(空)"

            print(f"\n{desc} (第{row_num}行第{col_num}列): '{value}'")

            if cell.font and cell.font.color:
                color = cell.font.color
                print(f"  字体颜色对象: {color}")

                # 检查RGB颜色
                if hasattr(color, 'rgb') and color.rgb:
                    rgb = color.rgb
                    if isinstance(rgb, str):
                        if len(rgb) == 8:
                            rgb = rgb[2:]  # 移除alpha
                        print(f"  RGB: #{rgb}")
                        if rgb.upper() == 'FF0000':
                            print(f"  ✅ 正确的红色!")
                        else:
                            print(f"  ❌ 不是红色: #{rgb}")
                    else:
                        print(f"  RGB类型错误: {type(rgb)}")

                # 检查主题颜色
                elif hasattr(color, 'theme') and color.theme is not None:
                    print(f"  主题颜色: {color.theme}")
                    if color.theme == 1:
                        print(f"  ❌ 主题1是黑色，不是红色")
                    else:
                        print(f"  ⚠️ 其他主题颜色")

                else:
                    print(f"  ❌ 无法识别的颜色格式")
            else:
                print(f"  ❌ 没有字体颜色信息")

        print("\n=== 测试样式提取函数 ===")

        def extract_cell_style_debug(cell):
            """调试版本的样式提取"""
            style = {}

            if cell.font:
                if cell.font.name:
                    style['font-family'] = cell.font.name
                if cell.font.size:
                    style['font-size'] = f"{cell.font.size}pt"
                if cell.font.bold:
                    style['font-weight'] = 'bold'
                if cell.font.italic:
                    style['font-style'] = 'italic'
                if cell.font.color:
                    try:
                        color = cell.font.color

                        # 处理RGB颜色
                        if color.rgb and isinstance(color.rgb, str):
                            color_rgb = color.rgb
                            if len(color_rgb) == 8:
                                color_rgb = color_rgb[2:]
                            elif len(color_rgb) == 6:
                                pass
                            else:
                                color_rgb = None

                            if color_rgb and len(color_rgb) == 6:
                                style['color'] = f"#{color_rgb}"

                        # 处理主题颜色
                        elif hasattr(color, 'theme') and color.theme is not None:
                            theme_colors = {
                                0: '#FFFFFF',  # 白色
                                1: '#000000',  # 黑色
                                2: '#1F497D',  # 深蓝色
                                3: '#4F81BD',  # 蓝色
                                4: '#9CBB58',  # 绿色
                                5: '#8064A2',  # 紫色
                                6: '#F79646',  # 橙色
                                7: '#4BACC6',  # 青色
                                8: '#F2F2F2',  # 浅灰色
                                9: '#808080',  # 灰色
                            }

                            if color.theme in theme_colors:
                                style['color'] = theme_colors[color.theme]
                            else:
                                style['color'] = '#000000'

                        else:
                            style['color'] = '#000000'

                    except Exception:
                        style['color'] = '#000000'
                else:
                    style['color'] = '#000000'

            style['background-color'] = '#FFFFFF'
            return style

        # 测试红色单元格的样式提取
        for row_num, col_num, desc in red_cells_to_check:
            cell = ws.cell(row=row_num, column=col_num)
            style = extract_cell_style_debug(cell)
            value = cell.value if cell.value else "(空)"
            print(f"\n{desc} '{value}' 样式提取结果: {style}")

            if 'color' in style:
                color = style['color']
                if color == '#FF0000':
                    print(f"  ✅ 样式提取正确：红色")
                elif color == '#000000':
                    print(f"  ❌ 样式提取错误：变成了黑色")
                else:
                    print(f"  ⚠️ 样式提取结果：其他颜色 {color}")
    else:
        print("❌ 没有找到Excel文件")
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 测试完成 ===")
