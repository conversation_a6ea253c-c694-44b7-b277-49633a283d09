import openpyxl

# 检查RGB颜色处理问题
print("=== 检查RGB颜色处理问题 ===")

# 检查样式提取函数中的RGB处理
def test_rgb_processing():
    """测试RGB颜色处理"""
    
    # 模拟不同的RGB值
    test_cases = [
        "FFFF0000",  # 8位，带alpha的红色
        "FF0000",    # 6位，纯红色
        "FF000000",  # 8位，带alpha的黑色
        "000000",    # 6位，纯黑色
    ]
    
    print("测试RGB颜色处理逻辑:")
    for rgb_value in test_cases:
        print(f"\n输入RGB: {rgb_value}")
        
        # 模拟当前的处理逻辑
        color_rgb = rgb_value
        if len(color_rgb) == 8:
            # 移除alpha通道，只保留RGB
            color_rgb = color_rgb[2:]
            print(f"  移除alpha后: {color_rgb}")
        elif len(color_rgb) == 6:
            # 已经是RGB格式
            print(f"  已是RGB格式: {color_rgb}")
        else:
            color_rgb = None
            print(f"  格式错误")

        if color_rgb and len(color_rgb) == 6:
            final_color = f"#{color_rgb}"
            print(f"  最终颜色: {final_color}")
        else:
            print(f"  处理失败")

test_rgb_processing()

print("\n=== 检查实际模板文件 ===")

# 加载模板文件
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

# 检查红色单元格的详细信息
red_cells = [(3, 3, "{{name}}"), (7, 2, "{{creator}}")]

for row, col, placeholder in red_cells:
    cell = ws.cell(row=row, column=col)
    print(f"\n{placeholder} (第{row}行第{col}列):")
    print(f"  值: '{cell.value}'")
    
    if cell.font and cell.font.color:
        color = cell.font.color
        print(f"  颜色对象: {color}")
        print(f"  颜色类型: {getattr(color, 'type', 'unknown')}")
        
        if hasattr(color, 'rgb') and color.rgb:
            rgb_value = color.rgb
            print(f"  原始RGB: {rgb_value}")
            print(f"  RGB类型: {type(rgb_value)}")
            
            # 尝试转换为字符串
            try:
                if hasattr(rgb_value, 'value'):
                    rgb_str = rgb_value.value
                    print(f"  RGB.value: {rgb_str}")
                elif hasattr(rgb_value, '__str__'):
                    rgb_str = str(rgb_value)
                    print(f"  RGB字符串: {rgb_str}")
                else:
                    print(f"  无法转换RGB为字符串")
            except Exception as e:
                print(f"  RGB转换错误: {e}")

print("\n=== 修复建议 ===")
print("问题可能在于:")
print("1. RGB对象不是字符串类型，需要特殊处理")
print("2. 需要检查RGB对象的实际结构")
print("3. 可能需要使用不同的方法获取RGB值")

print("\n=== 测试修复方案 ===")

def extract_color_fixed(color):
    """修复版本的颜色提取"""
    try:
        # 处理RGB颜色
        if hasattr(color, 'rgb') and color.rgb:
            rgb = color.rgb
            print(f"  RGB对象: {rgb}, 类型: {type(rgb)}")
            
            # 尝试多种方法获取RGB值
            rgb_str = None
            
            # 方法1: 直接转换
            if isinstance(rgb, str):
                rgb_str = rgb
                print(f"  方法1成功: {rgb_str}")
            
            # 方法2: 检查value属性
            elif hasattr(rgb, 'value'):
                rgb_str = rgb.value
                print(f"  方法2成功: {rgb_str}")
            
            # 方法3: 检查hex属性
            elif hasattr(rgb, 'hex'):
                rgb_str = rgb.hex
                print(f"  方法3成功: {rgb_str}")
            
            # 方法4: 强制转换为字符串
            else:
                try:
                    rgb_str = str(rgb)
                    print(f"  方法4成功: {rgb_str}")
                except:
                    print(f"  方法4失败")
            
            if rgb_str:
                # 处理RGB字符串
                if len(rgb_str) == 8:
                    rgb_str = rgb_str[2:]  # 移除alpha
                elif len(rgb_str) == 6:
                    pass  # 已经是正确格式
                else:
                    print(f"  RGB长度错误: {len(rgb_str)}")
                    return None
                
                if len(rgb_str) == 6:
                    final_color = f"#{rgb_str}"
                    print(f"  最终颜色: {final_color}")
                    return final_color
        
        return None
    except Exception as e:
        print(f"  颜色提取错误: {e}")
        return None

# 测试修复方案
for row, col, placeholder in red_cells:
    cell = ws.cell(row=row, column=col)
    if cell.font and cell.font.color:
        print(f"\n测试修复 {placeholder}:")
        result = extract_color_fixed(cell.font.color)
        if result:
            print(f"  ✅ 修复成功: {result}")
        else:
            print(f"  ❌ 修复失败")

print("\n=== 检查完成 ===")
