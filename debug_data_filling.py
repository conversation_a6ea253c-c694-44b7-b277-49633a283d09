import requests
import openpyxl
import time
import os

# 发送测试数据
data = {
    "data": {
        "list": [
            {
                "batno": "TEST001",
                "name": "测试商品1",
                "matrial": "塑料",
                "unit": "个"
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 发送测试数据 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

# 等待处理完成
time.sleep(1)

# 检查生成的临时文件
print("\n=== 检查生成的文件 ===")
filled_files = []
if os.path.exists('filled_outputs'):
    filled_files = [f for f in os.listdir('filled_outputs') if f.endswith('.xlsx')]
    filled_files.sort(key=lambda x: os.path.getctime(os.path.join('filled_outputs', x)), reverse=True)

if filled_files:
    latest_file = os.path.join('filled_outputs', filled_files[0])
    print(f"最新文件: {latest_file}")
    
    # 检查文件内容
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 检查填充后的数据 ===")
    for row in range(1, min(8, ws.max_row + 1)):
        row_data = []
        for col in range(1, min(10, ws.max_column + 1)):
            cell = ws.cell(row=row, column=col)
            value = cell.value if cell.value else ''
            row_data.append(str(value)[:15])
        print(f'第{row}行: {row_data}')
    
    print("\n=== 重点检查第5行 ===")
    row_5_data = {}
    for col in range(1, 10):
        cell = ws.cell(row=5, column=col)
        value = cell.value if cell.value else ''
        row_5_data[col] = value
        print(f"第5行第{col}列: '{value}'")
    
    print("\n=== 验证数据位置 ===")
    expected = {
        2: "TEST001",  # batno
        3: "测试商品1",  # name
        4: "塑料",      # matrial
        5: "个"        # unit
    }
    
    all_correct = True
    for col, expected_value in expected.items():
        actual_value = row_5_data.get(col, '')
        if str(actual_value) == str(expected_value):
            print(f"✅ 第{col}列正确: {expected_value}")
        else:
            print(f"❌ 第{col}列错误: 期望 '{expected_value}', 实际 '{actual_value}'")
            all_correct = False
    
    if all_correct:
        print("\n🎉 数据填充完全正确！")
    else:
        print("\n⚠️ 数据填充有问题")
        
        # 查找数据实际位置
        print("\n=== 查找数据实际位置 ===")
        for expected_value in expected.values():
            found = False
            for row in range(1, ws.max_row + 1):
                for col in range(1, ws.max_column + 1):
                    cell_value = ws.cell(row=row, column=col).value
                    if str(cell_value) == str(expected_value):
                        print(f"'{expected_value}' 找到在第{row}行第{col}列")
                        found = True
                        break
                if found:
                    break
            if not found:
                print(f"'{expected_value}' 未找到")
else:
    print("❌ 没有找到生成的文件")
