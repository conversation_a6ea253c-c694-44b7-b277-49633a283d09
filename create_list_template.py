from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "列表模板示例"

# 设置单元格样式
header_font = Font(name='宋体', size=12, bold=True)
normal_font = Font(name='宋体', size=11)
border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 添加表头信息
ws['A1'] = "订单信息"
ws['A1'].font = header_font

ws['A3'] = "订单号: {{order_id}}"
ws['A4'] = "客户名称: {{customer_name}}"
ws['A5'] = "订单日期: {{order_date}}"

# 方法1：固定数量的列表项
ws['A7'] = "商品清单（固定格式）:"
ws['A7'].font = header_font

ws['A8'] = "序号"
ws['B8'] = "商品名称"
ws['C8'] = "数量"
ws['D8'] = "单价"
ws['E8'] = "小计"

# 设置表头样式
for col in ['A8', 'B8', 'C8', 'D8', 'E8']:
    ws[col].font = header_font
    ws[col].border = border

# 固定的商品行
ws['A9'] = "1"
ws['B9'] = "{{items1_name}}"
ws['C9'] = "{{items1_quantity}}"
ws['D9'] = "{{items1_price}}"
ws['E9'] = "{{items1_total}}"

ws['A10'] = "2"
ws['B10'] = "{{items2_name}}"
ws['C10'] = "{{items2_quantity}}"
ws['D10'] = "{{items2_price}}"
ws['E10'] = "{{items2_total}}"

ws['A11'] = "3"
ws['B11'] = "{{items3_name}}"
ws['C11'] = "{{items3_quantity}}"
ws['D11'] = "{{items3_price}}"
ws['E11'] = "{{items3_total}}"

# 设置数据行样式
for row in range(9, 12):
    for col in ['A', 'B', 'C', 'D', 'E']:
        cell = f"{col}{row}"
        ws[cell].font = normal_font
        ws[cell].border = border

# 方法2：动态列表（使用特殊标记）
ws['A13'] = "动态商品清单:"
ws['A13'].font = header_font

ws['A14'] = "{{list:items}}"  # 这个标记表示从这里开始插入动态列表

# 合计
ws['A16'] = "总计: {{total_amount}}"
ws['A16'].font = header_font

# 调整列宽
ws.column_dimensions['A'].width = 8
ws.column_dimensions['B'].width = 20
ws.column_dimensions['C'].width = 10
ws.column_dimensions['D'].width = 10
ws.column_dimensions['E'].width = 12

# 保存工作簿
wb.save("templates/list_example.xlsx")
print("列表示例模板已创建: templates/list_example.xlsx")
