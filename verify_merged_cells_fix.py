import openpyxl
import glob
import os

# 找到最新生成的Excel文件
excel_files = glob.glob("filled_outputs/filled_delivery.xlsx_*.xlsx")
if excel_files:
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"检查最新文件: {latest_file}")
    
    # 加载Excel文件
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 验证合并单元格修复 ===")
    
    # 检查合并单元格
    merged_ranges = list(ws.merged_cells.ranges)
    print(f"合并单元格数量: {len(merged_ranges)}")
    
    print("\n所有合并单元格:")
    for i, merged_range in enumerate(merged_ranges):
        print(f"  {i+1}. {merged_range}")
    
    # 检查数据行
    print("\n=== 检查数据内容 ===")
    print("数据行内容:")
    for row_num in range(5, 9):  # 检查第5-8行（数据行）
        row_data = []
        for col_num in range(1, 6):  # 前5列
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value is not None else "(空)"
            row_data.append(str(value))
        print(f"  第{row_num}行: {' | '.join(row_data)}")
    
    # 检查合计行
    print("\n=== 检查合计行 ===")
    total_row = 9  # 合计行应该在第9行（原来第6行 + 插入3行）
    
    # 检查合计行的内容
    total_cell = ws.cell(row=total_row, column=1)
    print(f"合计行内容: '{total_cell.value}'")
    
    # 检查合计行是否被正确合并
    total_merged = False
    total_merge_range = None
    for merged_range in merged_ranges:
        if total_row >= merged_range.min_row and total_row <= merged_range.max_row:
            if 1 >= merged_range.min_col and 1 <= merged_range.max_col:
                total_merged = True
                total_merge_range = merged_range
                break
    
    if total_merged:
        print(f"✅ 合计行正确合并: {total_merge_range}")
        # 检查合并范围是否正确
        if total_merge_range.min_col == 1 and total_merge_range.max_col == 5:
            print("✅ 合并范围正确: A列到E列")
        else:
            print(f"❌ 合并范围错误: 第{total_merge_range.min_col}列到第{total_merge_range.max_col}列")
    else:
        print("❌ 合计行没有合并")
    
    # 检查制单人行
    print("\n=== 检查制单人行 ===")
    creator_row = 10  # 制单人行应该在第10行
    
    creator_label = ws.cell(row=creator_row, column=1)
    creator_value = ws.cell(row=creator_row, column=2)
    
    print(f"制单人标签: '{creator_label.value}'")
    print(f"制单人值: '{creator_value.value}'")
    
    # 检查制单人值是否为红色
    if creator_value.font and creator_value.font.color:
        color = creator_value.font.color
        if hasattr(color, 'rgb') and color.rgb:
            rgb_value = color.rgb
            if rgb_value == 'FFFF0000':
                print("✅ 制单人字体为红色")
            else:
                print(f"❌ 制单人字体颜色错误: {rgb_value}")
        else:
            print("❌ 制单人字体颜色信息缺失")
    else:
        print("❌ 制单人字体信息缺失")
    
    print("\n=== 验证结果总结 ===")
    
    # 统计验证结果
    checks = []
    
    # 检查1: 数据行数量
    data_rows_count = 4
    actual_data_rows = 0
    for row_num in range(5, 9):
        cell = ws.cell(row=row_num, column=1)
        if cell.value and str(cell.value).isdigit():
            actual_data_rows += 1
    
    if actual_data_rows == data_rows_count:
        checks.append("✅ 数据行数量正确")
    else:
        checks.append(f"❌ 数据行数量错误: 期望{data_rows_count}, 实际{actual_data_rows}")
    
    # 检查2: 合计行合并
    if total_merged:
        checks.append("✅ 合计行正确合并")
    else:
        checks.append("❌ 合计行合并失败")
    
    # 检查3: 制单人显示
    if creator_value.value and "合并测试员" in str(creator_value.value):
        checks.append("✅ 制单人正确显示")
    else:
        checks.append("❌ 制单人显示错误")
    
    # 输出结果
    for check in checks:
        print(check)
    
    success_count = sum(1 for check in checks if check.startswith("✅"))
    total_count = len(checks)
    
    if success_count == total_count:
        print(f"\n🎉 合并单元格修复完全成功！({success_count}/{total_count})")
    else:
        print(f"\n⚠️ 部分功能需要改进 ({success_count}/{total_count})")
    
    print(f"\n📁 Excel文件位置: {latest_file}")
    
else:
    print("❌ 没有找到Excel文件")

print("\n=== 验证完成 ===")
