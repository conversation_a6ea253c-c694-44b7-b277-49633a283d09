from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "发货单模板"

# 定义样式
title_font = Font(name='宋体', size=16, bold=True)
header_font = Font(name='宋体', size=12, bold=True, color='FFFFFF')
normal_font = Font(name='宋体', size=10)
small_font = Font(name='宋体', size=9)

# 定义填充色
header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
alt_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')

# 定义对齐方式
center_align = Alignment(horizontal='center', vertical='center')
left_align = Alignment(horizontal='left', vertical='center')
right_align = Alignment(horizontal='right', vertical='center')

# 定义边框
thin_border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 创建标题
ws.merge_cells('A1:G1')
ws['A1'] = "发货单"
ws['A1'].font = title_font
ws['A1'].alignment = center_align
ws.row_dimensions[1].height = 35

# 基本信息区域
ws['A3'] = "客户名称:"
ws['A3'].font = Font(name='宋体', size=11, bold=True)
ws['B3'] = "{{name}}"
ws['B3'].font = normal_font

ws['E3'] = "制单人:"
ws['E3'].font = Font(name='宋体', size=11, bold=True)
ws['F3'] = "{{creator}}"
ws['F3'].font = normal_font

ws['A4'] = "发货日期:"
ws['A4'].font = Font(name='宋体', size=11, bold=True)
ws['B4'] = "{{delivery_date}}"
ws['B4'].font = normal_font

# 商品清单标题
ws['A6'] = "商品清单:"
ws['A6'].font = Font(name='宋体', size=12, bold=True)

# 动态列表标记
ws['A7'] = "{{list:list}}"
ws['A7'].font = small_font

# 列表模板行（第8行）- 这一行定义了列表的格式
ws['A8'] = "{{item.batno}}"      # 批次号
ws['B8'] = "{{item.name}}"       # 名称
ws['C8'] = "{{item.matrial}}"    # 材质
ws['D8'] = "{{item.unit}}"       # 单位
ws['E8'] = "{{item.quantity}}"   # 数量
ws['F8'] = "{{item.note}}"       # 备注
ws['G8'] = "{{item.delivery_f}}" # 发货标志

# 设置列表表头（第7行下面插入）
headers = ['批次号', '名称', '材质', '单位', '数量', '备注', '发货标志']
for i, header in enumerate(headers):
    cell = ws.cell(row=7, column=i+1, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = center_align
    cell.border = thin_border

# 设置模板行样式
for col in range(1, 8):
    cell = ws.cell(row=8, column=col)
    cell.font = normal_font
    cell.alignment = left_align if col in [1, 2, 3, 6] else center_align
    cell.border = thin_border

# 列表后的内容（这些会在动态插入列表后自动向下移动）
ws['A10'] = "发货说明:"
ws['A10'].font = Font(name='宋体', size=11, bold=True)

ws.merge_cells('A11:G11')
ws['A11'] = "{{delivery_note}}"
ws['A11'].font = normal_font
ws['A11'].alignment = left_align

ws['A13'] = "发货人签名: ________________"
ws['A13'].font = normal_font

ws['E13'] = "收货人签名: ________________"
ws['E13'].font = normal_font

ws['A15'] = "发货时间: ________________"
ws['A15'].font = normal_font

ws['E15'] = "收货时间: ________________"
ws['E15'].font = normal_font

# 设置列宽
column_widths = {
    'A': 12,  # 批次号
    'B': 20,  # 名称
    'C': 12,  # 材质
    'D': 8,   # 单位
    'E': 8,   # 数量
    'F': 15,  # 备注
    'G': 10   # 发货标志
}

for col, width in column_widths.items():
    ws.column_dimensions[col].width = width

# 设置行高
ws.row_dimensions[7].height = 25  # 表头行
ws.row_dimensions[8].height = 22  # 模板行
for row in range(10, 16):
    ws.row_dimensions[row].height = 20

# 保存工作簿
wb.save("templates/delivery.xlsx")
print("发货单模板已创建: templates/delivery.xlsx")
print("\n模板说明:")
print("1. {{list:list}} - 动态列表标记，系统会在此处插入列表数据")
print("2. {{item.字段名}} - 列表项字段占位符")
print("3. 列表下方的内容会自动向下移动")
print("4. 支持任意数量的列表项")
