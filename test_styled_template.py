import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 测试样式丰富的模板
print("=== 测试样式丰富的模板 ===")
data = {
    "template_name": "styled_template",
    "data": {
        "company_name": "北京科技发展有限公司",
        "invoice_no": "INV-2023-12-001",
        "invoice_date": "2023年12月01日",
        "customer_name": "上海贸易有限公司",
        "customer_phone": "021-12345678",
        "items1_name": "高端笔记本电脑",
        "items1_spec": "ThinkPad X1 Carbon",
        "items1_quantity": "2台",
        "items1_price": "¥12,000.00",
        "items1_total": "¥24,000.00",
        "items2_name": "无线办公套装",
        "items2_spec": "罗技MX Master 3套装",
        "items2_quantity": "2套",
        "items2_price": "¥800.00",
        "items2_total": "¥1,600.00",
        "items3_name": "显示器支架",
        "items3_spec": "双屏支架可调节",
        "items3_quantity": "2个",
        "items3_price": "¥300.00",
        "items3_total": "¥600.00",
        "total_chinese": "贰万陆仟贰佰元整",
        "total_amount": "¥26,200.00",
        "remarks": "货到付款，质保期一年，如有质量问题请及时联系售后服务。",
        "issuer": "张三",
        "reviewer": "李四"
    }
}

response = requests.post(url, json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n请访问 http://localhost:5000/display 查看样式效果")
    print("注意观察以下样式是否正确显示：")
    print("1. 标题：红色大字体，居中对齐")
    print("2. 表头：蓝色背景，白色字体")
    print("3. 数据行：交替背景色")
    print("4. 价格：蓝色字体")
    print("5. 金额：红色粗体")
    print("6. 合计行：黄色背景，特殊字体")
    print("7. 列宽和行高：与Excel模板一致")
    print("8. 字体：不同区域使用不同字体和大小")
