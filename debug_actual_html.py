import requests

# 发送测试数据
data = {
    "data": {
        "list": [
            {
                "batno": "DEBUG001",
                "name": "调试商品",
                "matrial": "测试材料",
                "unit": "件"
            }
        ],
        "name": "调试客户",
        "creator": "调试员"
    },
    "template_name": "delivery"
}

print("=== 发送调试数据 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 获取显示页面的HTML
    display_response = requests.get("http://localhost:5000/display")
    html_content = display_response.text
    
    # 保存HTML到文件以便检查
    with open('debug_output.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("HTML已保存到 debug_output.html")
    
    # 查找表格部分
    import re
    table_match = re.search(r'<table[^>]*>.*?</table>', html_content, re.DOTALL)
    if table_match:
        table_html = table_match.group(0)
        print("\n=== 表格HTML ===")
        
        # 分析表格行
        rows = re.findall(r'<tr[^>]*>(.*?)</tr>', table_html, re.DOTALL)
        for i, row in enumerate(rows):
            print(f"\n第{i+1}行:")
            # 提取单元格
            cells = re.findall(r'<t[hd][^>]*>(.*?)</t[hd]>', row, re.DOTALL)
            for j, cell in enumerate(cells):
                # 清理HTML标签
                clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                if clean_cell == '&nbsp;':
                    clean_cell = '(空)'
                print(f"  第{j+1}列: '{clean_cell}'")
        
        # 重点检查第5行
        if len(rows) >= 5:
            row_5 = rows[4]  # 第5行，索引为4
            print(f"\n=== 重点分析第5行 ===")
            print(f"第5行HTML: {row_5}")
            
            cells_5 = re.findall(r'<t[hd][^>]*>(.*?)</t[hd]>', row_5, re.DOTALL)
            print(f"第5行单元格数量: {len(cells_5)}")
            
            expected_values = ["(空)", "DEBUG001", "调试商品", "测试材料", "件"]
            print(f"期望值: {expected_values}")
            
            actual_values = []
            for cell in cells_5:
                clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                if clean_cell == '&nbsp;' or clean_cell == '':
                    clean_cell = '(空)'
                actual_values.append(clean_cell)
            
            print(f"实际值: {actual_values}")
            
            # 检查是否匹配
            if len(actual_values) >= len(expected_values):
                match = True
                for i, (exp, act) in enumerate(zip(expected_values, actual_values)):
                    if exp != act:
                        print(f"❌ 第{i+1}列不匹配: 期望 '{exp}', 实际 '{act}'")
                        match = False
                
                if match:
                    print("✅ 第5行数据完全正确！")
                else:
                    print("❌ 第5行数据有误")
            else:
                print(f"❌ 第5行单元格数量不足: 期望至少{len(expected_values)}个，实际{len(actual_values)}个")
    else:
        print("❌ 未找到表格HTML")
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 检查完成 ===")
print("请查看 debug_output.html 文件以获取完整的HTML内容")
