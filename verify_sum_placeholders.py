import openpyxl
import glob
import os

# 找到最新生成的Excel文件
excel_files = glob.glob("filled_outputs/filled_delivery_with_sum_placeholders.xlsx_*.xlsx")
if excel_files:
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"检查最新文件: {latest_file}")
    
    # 加载Excel文件
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 验证 {{sum.field}} 占位符功能 ===")
    
    # 检查数据行
    print("数据行内容:")
    data_rows = []
    for row_num in range(5, 9):  # 检查第5-8行（数据行）
        row_data = []
        for col_num in range(1, 10):  # 所有列
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value is not None else "(空)"
            row_data.append(str(value))
        
        print(f"  第{row_num}行: {' | '.join(row_data)}")
        
        # 提取数量和金额用于验证
        qty = ws.cell(row=row_num, column=6).value
        amount = ws.cell(row=row_num, column=8).value
        
        try:
            qty_num = float(qty) if qty and str(qty).replace('.', '').isdigit() else 0
            amount_num = float(amount) if amount and str(amount).replace('.', '').isdigit() else 0
            data_rows.append({'qty': qty_num, 'amount': amount_num})
        except:
            data_rows.append({'qty': 0, 'amount': 0})
    
    # 计算预期的总和
    expected_qty_sum = sum(row['qty'] for row in data_rows)
    expected_amount_sum = sum(row['amount'] for row in data_rows)
    
    print(f"\n📊 预期计算结果:")
    print(f"数量总和: {expected_qty_sum}")
    print(f"金额总和: {expected_amount_sum}")
    
    # 检查合计行
    print("\n=== 检查合计行 ===")
    total_row = 9  # 合计行应该在第9行
    
    print(f"合计行内容:")
    row_data = []
    for col_num in range(1, 10):  # 所有列
        cell = ws.cell(row=total_row, column=col_num)
        value = cell.value if cell.value is not None else "(空)"
        row_data.append(str(value))
    print(f"  第{total_row}行: {' | '.join(row_data)}")
    
    # 检查SUM占位符的计算结果
    qty_sum_cell = ws.cell(row=total_row, column=6)  # 数量合计
    amount_sum_cell = ws.cell(row=total_row, column=8)  # 金额合计
    
    actual_qty_sum = qty_sum_cell.value
    actual_amount_sum = amount_sum_cell.value
    
    print(f"\n🧮 SUM占位符计算结果:")
    print(f"{{{{sum.qty}}}} 结果: {actual_qty_sum}")
    print(f"{{{{sum.amount}}}} 结果: {actual_amount_sum}")
    
    # 验证计算结果
    print(f"\n=== 验证结果 ===")
    
    checks = []
    
    # 检查1: 数量求和
    try:
        if float(actual_qty_sum) == expected_qty_sum:
            checks.append("✅ 数量求和正确")
        else:
            checks.append(f"❌ 数量求和错误: 期望{expected_qty_sum}, 实际{actual_qty_sum}")
    except:
        checks.append(f"❌ 数量求和格式错误: {actual_qty_sum}")
    
    # 检查2: 金额求和
    try:
        if float(actual_amount_sum) == expected_amount_sum:
            checks.append("✅ 金额求和正确")
        else:
            checks.append(f"❌ 金额求和错误: 期望{expected_amount_sum}, 实际{actual_amount_sum}")
    except:
        checks.append(f"❌ 金额求和格式错误: {actual_amount_sum}")
    
    # 检查3: 数据行数量
    if len(data_rows) == 4:
        checks.append("✅ 数据行数量正确")
    else:
        checks.append(f"❌ 数据行数量错误: 期望4, 实际{len(data_rows)}")
    
    # 检查4: SUM占位符在数据行中是否为空
    sum_in_data_rows = False
    for row_num in range(5, 9):
        qty_cell = ws.cell(row=row_num, column=6)
        amount_cell = ws.cell(row=row_num, column=8)
        
        # 检查是否包含SUM占位符（应该不包含）
        if (qty_cell.value and "sum" in str(qty_cell.value).lower()) or \
           (amount_cell.value and "sum" in str(amount_cell.value).lower()):
            sum_in_data_rows = True
            break
    
    if not sum_in_data_rows:
        checks.append("✅ 数据行中SUM占位符正确处理")
    else:
        checks.append("❌ 数据行中仍包含SUM占位符")
    
    # 输出结果
    for check in checks:
        print(check)
    
    success_count = sum(1 for check in checks if check.startswith("✅"))
    total_count = len(checks)
    
    if success_count == total_count:
        print(f"\n🎉 {{{{sum.field}}}} 占位符功能完全成功！({success_count}/{total_count})")
        print("   ✅ 自动计算正确")
        print("   ✅ 数据处理正确")
        print("   ✅ 位置显示正确")
        print("   ✅ 格式处理正确")
    else:
        print(f"\n⚠️ 部分功能需要改进 ({success_count}/{total_count})")
    
    print(f"\n📁 Excel文件位置: {latest_file}")
    
    # 详细的计算过程
    print(f"\n📊 详细计算过程:")
    print("数量计算:")
    for i, row in enumerate(data_rows):
        print(f"  第{i+1}行: {row['qty']}")
    print(f"  总和: {expected_qty_sum}")
    
    print("金额计算:")
    for i, row in enumerate(data_rows):
        print(f"  第{i+1}行: {row['amount']}")
    print(f"  总和: {expected_amount_sum}")
    
else:
    print("❌ 没有找到delivery_with_sum_placeholders的Excel文件")
    
    # 检查是否有其他Excel文件
    all_excel_files = glob.glob("filled_outputs/filled_*.xlsx")
    if all_excel_files:
        print("\n找到的其他Excel文件:")
        for file in all_excel_files[-3:]:  # 显示最新的3个文件
            print(f"  {file}")

print("\n=== 验证完成 ===")
