import openpyxl

# 测试主题颜色处理
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

def debug_extract_cell_style(cell):
    """调试版本的样式提取函数"""
    style = {}

    print(f"调试单元格 ({cell.row}, {cell.column}): '{cell.value}'")

    # 字体样式
    if cell.font:
        print(f"  字体对象存在")
        if cell.font.name:
            style['font-family'] = cell.font.name
            print(f"  字体名称: {cell.font.name}")
        if cell.font.size:
            style['font-size'] = f"{cell.font.size}pt"
            print(f"  字体大小: {cell.font.size}pt")
        if cell.font.bold:
            style['font-weight'] = 'bold'
            print(f"  粗体: True")
        if cell.font.italic:
            style['font-style'] = 'italic'
            print(f"  斜体: True")

        # 详细调试字体颜色
        if cell.font.color:
            print(f"  字体颜色对象存在")
            color = cell.font.color
            print(f"  颜色对象: {color}")
            print(f"  颜色类型: {getattr(color, 'type', 'unknown')}")
            print(f"  RGB: {getattr(color, 'rgb', 'None')}")
            print(f"  主题: {getattr(color, 'theme', 'None')}")
            print(f"  色调: {getattr(color, 'tint', 'None')}")
            print(f"  自动: {getattr(color, 'auto', 'None')}")
            print(f"  索引: {getattr(color, 'indexed', 'None')}")

            try:
                # 处理RGB颜色
                if color.rgb and isinstance(color.rgb, str):
                    color_rgb = color.rgb
                    print(f"  处理RGB颜色: {color_rgb}")
                    if len(color_rgb) == 8:
                        # 移除alpha通道，只保留RGB
                        color_rgb = color_rgb[2:]
                        print(f"  移除alpha后: {color_rgb}")
                    elif len(color_rgb) == 6:
                        # 已经是RGB格式
                        print(f"  已是RGB格式: {color_rgb}")
                    else:
                        color_rgb = None
                        print(f"  RGB格式错误")

                    if color_rgb and len(color_rgb) == 6:
                        style['color'] = f"#{color_rgb}"
                        print(f"  ✅ 设置RGB颜色: #{color_rgb}")

                # 处理主题颜色
                elif hasattr(color, 'theme') and color.theme is not None:
                    print(f"  处理主题颜色: theme={color.theme}")
                    # 主题颜色映射
                    theme_colors = {
                        0: '#FFFFFF',  # 白色
                        1: '#000000',  # 黑色
                        2: '#1F497D',  # 深蓝色
                        3: '#4F81BD',  # 蓝色
                        4: '#9CBB58',  # 绿色
                        5: '#8064A2',  # 紫色
                        6: '#F79646',  # 橙色
                        7: '#4BACC6',  # 青色
                        8: '#F2F2F2',  # 浅灰色
                        9: '#808080',  # 灰色
                    }

                    if color.theme in theme_colors:
                        style['color'] = theme_colors[color.theme]
                        print(f"  ✅ 设置主题颜色: theme={color.theme} -> {theme_colors[color.theme]}")
                    else:
                        # 未知主题颜色，默认使用黑色
                        style['color'] = '#000000'
                        print(f"  ✅ 未知主题颜色，使用默认黑色")

                # 处理自动颜色或其他情况
                else:
                    # 默认使用黑色
                    style['color'] = '#000000'
                    print(f"  ✅ 其他情况，使用默认黑色")

            except Exception as e:
                # 颜色处理出错，使用默认黑色
                style['color'] = '#000000'
                print(f"  ❌ 颜色处理出错: {e}，使用默认黑色")
        else:
            # 没有设置字体颜色，使用默认黑色
            style['color'] = '#000000'
            print(f"  ✅ 没有字体颜色，使用默认黑色")
    else:
        print(f"  没有字体对象")
        style['color'] = '#000000'

    print(f"  最终颜色: {style.get('color', '未设置')}")
    print()
    return style

# 测试几个关键单元格
test_cells = [
    (1, 1),  # 标题
    (4, 2),  # 表头
    (5, 2),  # 数据行
]

print("=== 调试字体颜色提取 ===")
for row, col in test_cells:
    cell = ws.cell(row=row, column=col)
    style = debug_extract_cell_style(cell)
    print(f"单元格 ({row},{col}) 最终样式: {style}")
    print("=" * 50)
