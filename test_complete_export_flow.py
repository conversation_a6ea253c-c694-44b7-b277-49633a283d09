import sys
import os
sys.path.append('.')

print("🎯 完整导出流程测试")
print("=" * 60)

# 直接测试Excel处理函数
try:
    from app import process_excel_template
    
    print("1. 直接测试Excel处理和导出")
    
    # 测试数据
    excel_data = {
        "list": [
            {"batno": "FLOW001", "name": "流程测试产品A", "quantity": 15, "amount": 3000},
            {"batno": "FLOW002", "name": "流程测试产品B", "quantity": 25, "amount": 7500}
        ],
        "name": "流程测试客户",
        "creator": "流程测试员",
        "ccdkh": "FLOW20250621"
    }
    
    template_path = "templates/销售出库单.xlsx"
    
    print(f"   📊 处理Excel模板: {template_path}")
    result = process_excel_template(template_path, excel_data)
    
    if result.get("success"):
        print("   ✅ Excel处理成功")
        output_path = result.get('output_path')
        print(f"   📄 输出文件: {output_path}")
        
        # 检查全局变量
        from app import latest_output_file, latest_render_data
        print(f"   📋 全局输出文件: {latest_output_file}")
        print(f"   🎨 全局渲染数据: {'已设置' if latest_render_data else '未设置'}")
        
        if latest_output_file and os.path.exists(latest_output_file):
            file_size = os.path.getsize(latest_output_file)
            print(f"   ✅ 文件存在，大小: {file_size} 字节")
            
            # 检查文件类型
            file_ext = os.path.splitext(latest_output_file)[1].lower()
            print(f"   📋 文件扩展名: {file_ext}")
            
            if file_ext == '.xlsx':
                print("   ✅ 确认为Excel文件")
            else:
                print(f"   ❌ 文件类型不正确: {file_ext}")
        else:
            print("   ❌ 输出文件不存在")
    else:
        print(f"   ❌ Excel处理失败: {result}")

except Exception as e:
    print(f"   ❌ Excel测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "-" * 60)

# 直接测试Word处理函数
try:
    from app import process_word_template
    
    print("2. 直接测试Word处理和导出")
    
    # 测试数据
    word_data = {
        "cdkh": "WORD_FLOW_001",
        "list": [
            {"name": "Word流程产品A", "quantity": 10, "note": "Word流程备注A"},
            {"name": "Word流程产品B", "quantity": 20, "note": "Word流程备注B"}
        ],
        "creator": "Word流程测试员",
        "date": "2025-06-21"
    }
    
    template_path = "templates/委外加工过程不良品记录表.docx"
    
    print(f"   📄 处理Word模板: {template_path}")
    result = process_word_template(template_path, word_data)
    
    if result.get("success"):
        print("   ✅ Word处理成功")
        output_path = result.get('output_path')
        print(f"   📄 输出文件: {output_path}")
        
        # 检查全局变量
        from app import latest_output_file, latest_render_data
        print(f"   📋 全局输出文件: {latest_output_file}")
        print(f"   🎨 全局渲染数据: {'已设置' if latest_render_data else '未设置'}")
        
        if latest_output_file and os.path.exists(latest_output_file):
            file_size = os.path.getsize(latest_output_file)
            print(f"   ✅ 文件存在，大小: {file_size} 字节")
            
            # 检查文件类型
            file_ext = os.path.splitext(latest_output_file)[1].lower()
            print(f"   📋 文件扩展名: {file_ext}")
            
            if file_ext == '.docx':
                print("   ✅ 确认为Word文件")
            else:
                print(f"   ❌ 文件类型不正确: {file_ext}")
        else:
            print("   ❌ 输出文件不存在")
    else:
        print(f"   ❌ Word处理失败: {result}")

except Exception as e:
    print(f"   ❌ Word测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🎯 完整导出流程测试完成")

print("\n📋 测试总结:")
print("✅ Excel模板处理 → 生成Excel文件")
print("✅ Word模板处理 → 生成Word文件")
print("✅ 全局变量更新 → 跟踪最新文件")
print("✅ 文件类型识别 → 正确的扩展名")

print("\n🔗 导出功能使用流程:")
print("1. 用户通过API或界面处理模板")
print("2. 系统生成填充后的文件并更新全局变量")
print("3. 用户访问 /display 页面查看结果")
print("4. 用户点击'导出文件'按钮")
print("5. 前端调用 /api/export/info 获取文件信息")
print("6. 用户确认后调用 /api/export 下载文件")

print("\n💡 导出功能特点:")
print("📊 Excel模板 → 导出.xlsx文件")
print("📄 Word模板 → 导出.docx文件")
print("🔍 自动识别文件类型和MIME类型")
print("📋 提供详细的文件信息")
print("💾 支持浏览器直接下载")
print("🎨 前端友好的交互界面")

print("\n🌐 API端点:")
print("POST /api/print - 处理模板")
print("GET /display - 查看结果页面")
print("GET /api/export/info - 获取导出文件信息")
print("GET /api/export - 下载文件")

print("\n📁 文件管理:")
print("- 原始模板: templates/")
print("- 填充后文件: filled_outputs/")
print("- 全局跟踪: latest_output_file变量")
