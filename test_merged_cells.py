import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 测试合并单元格模板
print("=== 测试合并单元格模板 ===")
data = {
    "template_name": "merged_cells_template",
    "data": {
        "order_id": "SO-2023-001",
        "order_date": "2023-12-01",
        "customer_name": "北京科技有限公司",
        "phone": "010-12345678",
        "items1_name": "笔记本电脑",
        "items1_spec": "ThinkPad X1",
        "items1_quantity": "2",
        "items1_price": "8000.00",
        "items1_total": "16000.00",
        "items2_name": "无线鼠标",
        "items2_spec": "罗技MX Master",
        "items2_quantity": "2",
        "items2_price": "500.00",
        "items2_total": "1000.00",
        "items3_name": "机械键盘",
        "items3_spec": "Cherry MX",
        "items3_quantity": "2",
        "items3_price": "800.00",
        "items3_total": "1600.00",
        "total_amount": "18600.00",
        "remarks": "请在收到货物后7天内付款，如有质量问题请及时联系。",
        "creator": "张三",
        "reviewer": "李四"
    }
}

response = requests.post(url, json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n请访问 http://localhost:5000/display 查看合并单元格的打印结果")
    print("注意观察：")
    print("1. 标题行是否正确合并")
    print("2. 订单信息行是否正确合并")
    print("3. 合计行是否正确合并")
    print("4. 备注和签名区域是否正确合并")
