import requests
import time

print("🎯 自增序号功能最终综合测试")
print("=" * 60)

# 测试1：自动检测方式（方式1）
print("\n📋 测试1：自动检测方式")
print("-" * 30)

data1 = {
    "data": {
        "list": [
            {"batno": "AUTO001", "name": "自动检测产品1", "matrial": "材料1", "unit": "个"},
            {"batno": "AUTO002", "name": "自动检测产品2", "matrial": "材料2", "unit": "套"},
            {"batno": "AUTO003", "name": "自动检测产品3", "matrial": "材料3", "unit": "件"}
        ],
        "name": "自动检测客户",
        "creator": "自动检测员"
    },
    "template_name": "delivery"  # 使用自动检测的模板
}

response1 = requests.post("http://localhost:5000/api/print", json=data1)
print(f"状态码: {response1.status_code}")
if response1.status_code == 200:
    print("✅ 自动检测方式测试成功")
    print("   - 第1列为空，系统自动添加序号")
    print("   - 预期序号：1, 2, 3")
else:
    print(f"❌ 自动检测方式测试失败: {response1.text}")

time.sleep(1)  # 等待1秒

# 测试2：{{auto_index}} 占位符方式（方式2）
print("\n📋 测试2：{{auto_index}} 占位符方式")
print("-" * 30)

data2 = {
    "data": {
        "list": [
            {"code": "FLEX001", "name": "灵活序号产品1", "spec": "规格1", "unit": "台"},
            {"code": "FLEX002", "name": "灵活序号产品2", "spec": "规格2", "unit": "套"},
            {"code": "FLEX003", "name": "灵活序号产品3", "spec": "规格3", "unit": "个"},
            {"code": "FLEX004", "name": "灵活序号产品4", "spec": "规格4", "unit": "件"}
        ],
        "name": "{{auto_index}} 占位符客户",
        "creator": "占位符测试员"
    },
    "template_name": "flexible_auto_index"  # 使用 {{auto_index}} 占位符的模板
}

response2 = requests.post("http://localhost:5000/api/print", json=data2)
print(f"状态码: {response2.status_code}")
if response2.status_code == 200:
    print("✅ {{auto_index}} 占位符方式测试成功")
    print("   - 第1列：纯序号 {{auto_index}}")
    print("   - 第6列：嵌入文本 第{{auto_index}}项")
    print("   - 预期序号：1, 2, 3, 4")
    print("   - 预期备注：第1项, 第2项, 第3项, 第4项")
else:
    print(f"❌ {{auto_index}} 占位符方式测试失败: {response2.text}")

print("\n🎨 功能对比")
print("-" * 30)
print("方式1 - 自动检测:")
print("  ✅ 简单易用，无需修改模板")
print("  ✅ 向后兼容，现有模板直接可用")
print("  ❌ 只能在第1列，格式固定")

print("\n方式2 - {{auto_index}} 占位符:")
print("  ✅ 灵活位置，可放在任意列")
print("  ✅ 支持文本嵌入，格式自定义")
print("  ✅ 可多次使用，功能强大")
print("  ❌ 需要修改模板")

print("\n🔗 查看链接:")
print("   📄 普通页面: http://localhost:5000/display")
print("   🖨️ 打印页面: http://localhost:5000/print")

print("\n📊 测试结果总结:")
if response1.status_code == 200 and response2.status_code == 200:
    print("🎉 两种自增序号方式都测试成功！")
    print("   ✅ 方式1：自动检测 - 正常工作")
    print("   ✅ 方式2：{{auto_index}} 占位符 - 正常工作")
    print("   ✅ 系统支持两种方式并存")
    print("   ✅ 功能完整，满足各种需求")
else:
    print("❌ 部分测试失败，需要检查")

print("\n💡 使用建议:")
print("1. 简单场景：使用自动检测（方式1）")
print("2. 复杂需求：使用 {{auto_index}} 占位符（方式2）")
print("3. 新项目：推荐使用 {{auto_index}} 占位符")
print("4. 现有项目：可以继续使用自动检测")

print("\n" + "=" * 60)
print("🎊 自增序号功能最终测试完成！")
print("系统现在支持两种强大的自增序号实现方式！")
