import openpyxl
from copy import copy

# 创建包含 {{sum.field}} 占位符的模板
print("=== 创建包含 {{sum.field}} 占位符的模板 ===")

# 加载原始模板
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("原始模板结构:")
for row in range(1, 8):
    row_data = []
    for col in range(1, 10):
        cell = ws.cell(row=row, column=col)
        value = cell.value if cell.value else "(空)"
        row_data.append(str(value)[:10])  # 限制长度
    print(f"  第{row}行: {' | '.join(row_data)}")

# 修改模板，添加数值字段和SUM占位符
print("\n=== 修改模板添加SUM占位符 ===")

# 在第6列（数量列）的合计行添加 {{sum.qty}} 占位符
qty_sum_cell = ws.cell(row=6, column=6)
qty_sum_cell.value = "{{sum.qty}}"
print(f"设置第6列合计行: {{sum.qty}}")

# 在第8列（金额列）的合计行添加 {{sum.amount}} 占位符
amount_sum_cell = ws.cell(row=6, column=8)
amount_sum_cell.value = "{{sum.amount}}"
print(f"设置第8列合计行: {{sum.amount}}")

# 保存新模板
new_template_path = 'templates/delivery_with_sum_placeholders.xlsx'
wb.save(new_template_path)
print(f"\n✅ 新模板已保存: {new_template_path}")

print("\n=== 新模板结构 ===")
print("第1列: {{auto_index}} - 自增序号")
print("第2列: {{list.batno}} - 批号")
print("第3列: {{list.name}} - 名称")
print("第4列: {{list.matrial}} - 材质")
print("第5列: {{list.unit}} - 单位")
print("第6列: {{list.qty}} - 数量 (数据行) / {{sum.qty}} - 数量合计 (合计行)")
print("第7列: {{list.price}} - 单价")
print("第8列: {{list.amount}} - 金额 (数据行) / {{sum.amount}} - 金额合计 (合计行)")
print("第9列: {{list.remark}} - 备注")

print("\n=== 使用说明 ===")
print("1. 使用模板名称: 'delivery_with_sum_placeholders'")
print("2. {{sum.qty}} 会自动计算所有数量的总和")
print("3. {{sum.amount}} 会自动计算所有金额的总和")
print("4. SUM占位符只在合计行显示，数据行显示为空")

print("\n=== JSON数据格式示例 ===")
print("""
{
  "template_name": "delivery_with_sum_placeholders",
  "data": {
    "list": [
      {"batno": "A001", "name": "产品A", "matrial": "材料A", "unit": "个", "qty": 10, "price": 100, "amount": 1000, "remark": "备注A"},
      {"batno": "B002", "name": "产品B", "matrial": "材料B", "unit": "套", "qty": 20, "price": 200, "amount": 4000, "remark": "备注B"}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
""")

print("\n=== 预期结果 ===")
print("序号 | 批号 | 名称   | 材质   | 单位 | 数量 | 单价 | 金额 | 备注")
print("-" * 70)
print(" 1   | A001 | 产品A  | 材料A  | 个   | 10   | 100  | 1000 | 备注A")
print(" 2   | B002 | 产品B  | 材料B  | 套   | 20   | 200  | 4000 | 备注B")
print("合计：                                | 30   |      | 5000 |")

# 验证新模板
print("\n=== 验证新模板 ===")
wb_verify = openpyxl.load_workbook(new_template_path)
ws_verify = wb_verify.active

print("验证合计行内容:")
for col in range(1, 10):
    cell = ws_verify.cell(row=6, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 检查是否包含SUM占位符
sum_placeholders_found = []
for col in range(1, ws_verify.max_column + 1):
    cell = ws_verify.cell(row=6, column=col)
    if cell.value and "{{sum." in str(cell.value):
        sum_placeholders_found.append({
            'col': col,
            'value': cell.value
        })

if sum_placeholders_found:
    print(f"\n✅ 找到 {len(sum_placeholders_found)} 个SUM占位符:")
    for placeholder in sum_placeholders_found:
        print(f"  第{placeholder['col']}列: {placeholder['value']}")
else:
    print("\n❌ 没有找到SUM占位符")

print("\n=== 创建完成 ===")
