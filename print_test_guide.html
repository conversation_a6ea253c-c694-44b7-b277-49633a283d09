<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印预览测试指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            background-color: #f9f9f9;
        }
        .problem {
            background-color: #ffebee;
            border-left-color: #f44336;
        }
        .solution {
            background-color: #e8f5e8;
            border-left-color: #4CAF50;
        }
        .steps {
            background-color: #e3f2fd;
            border-left-color: #2196F3;
        }
        ol, ul {
            margin-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ 打印预览黑色背景问题解决指南</h1>
        
        <div class="section problem">
            <h2>❌ 问题描述</h2>
            <p>在浏览器中点击"打印"或使用Ctrl+P时，打印预览显示黑色背景，而不是期望的白色背景。</p>
        </div>
        
        <div class="section solution">
            <h2>✅ 解决方案</h2>
            <p>我们已经在系统中实施了以下修复措施：</p>
            <ul>
                <li>添加了 <span class="highlight">@page</span> CSS规则强制设置页面背景</li>
                <li>使用 <span class="highlight">!important</span> 优先级覆盖浏览器默认样式</li>
                <li>设置 <span class="highlight">print-color-adjust: exact</span> 确保颜色准确</li>
                <li>移除所有阴影、背景图像和特效</li>
                <li>强制所有文本为黑色，背景为白色</li>
            </ul>
        </div>
        
        <div class="section steps">
            <h2>🔧 浏览器设置步骤</h2>
            
            <h3>Chrome浏览器：</h3>
            <ol>
                <li>打开打印预览（Ctrl+P）</li>
                <li>点击"更多设置"</li>
                <li>勾选 <span class="highlight">"背景图形"</span> 选项</li>
                <li>确保选择了正确的纸张大小（A4）</li>
                <li>边距设置为"默认"或"最小"</li>
            </ol>
            
            <h3>Firefox浏览器：</h3>
            <ol>
                <li>打开打印预览（Ctrl+P）</li>
                <li>在打印设置中找到"外观"部分</li>
                <li>勾选 <span class="highlight">"打印背景（颜色和图像）"</span></li>
                <li>选择合适的纸张大小</li>
            </ol>
            
            <h3>Edge浏览器：</h3>
            <ol>
                <li>打开打印预览（Ctrl+P）</li>
                <li>点击"更多设置"</li>
                <li>勾选 <span class="highlight">"背景图形"</span></li>
                <li>调整页边距为合适大小</li>
            </ol>
        </div>
        
        <div class="section">
            <h2>🎯 测试方法</h2>
            <ol>
                <li>访问 <a href="http://localhost:5000/display" target="_blank">http://localhost:5000/display</a></li>
                <li>按 <span class="highlight">Ctrl+P</span> 打开打印预览</li>
                <li>检查预览是否显示白色背景</li>
                <li>如果仍然是黑色，请按照上述浏览器设置步骤操作</li>
                <li>确认表格边框清晰可见</li>
                <li>确认文字清晰可读</li>
            </ol>
        </div>
        
        <div class="section">
            <h2>🔍 故障排除</h2>
            
            <h3>如果打印预览仍然是黑色：</h3>
            <ul>
                <li><strong>清除浏览器缓存：</strong> Ctrl+Shift+Delete</li>
                <li><strong>尝试无痕模式：</strong> Ctrl+Shift+N (Chrome) 或 Ctrl+Shift+P (Firefox)</li>
                <li><strong>更新浏览器：</strong> 确保使用最新版本</li>
                <li><strong>尝试其他浏览器：</strong> Chrome、Firefox、Edge</li>
                <li><strong>检查系统主题：</strong> 如果使用深色主题，可能影响打印预览</li>
            </ul>
            
            <h3>如果表格样式丢失：</h3>
            <ul>
                <li>确保勾选了"背景图形"选项</li>
                <li>检查页面是否完全加载</li>
                <li>刷新页面后重新打印</li>
            </ul>
        </div>
        
        <div class="section solution">
            <h2>✨ 最佳实践</h2>
            <ul>
                <li><strong>推荐浏览器：</strong> Chrome 或 Edge（对CSS打印支持最好）</li>
                <li><strong>纸张设置：</strong> A4，纵向</li>
                <li><strong>边距设置：</strong> 默认（约1.27cm）</li>
                <li><strong>缩放比例：</strong> 100%</li>
                <li><strong>背景图形：</strong> 必须勾选</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>📞 技术支持</h2>
            <p>如果按照以上步骤仍然无法解决问题，请联系技术支持并提供以下信息：</p>
            <ul>
                <li>使用的浏览器名称和版本</li>
                <li>操作系统版本</li>
                <li>打印预览的截图</li>
                <li>是否勾选了"背景图形"选项</li>
            </ul>
        </div>
    </div>
</body>
</html>
