import requests

# 测试 {{auto_index}} 占位符功能
data = {
    "data": {
        "list": [
            {"batno": "AUTO001", "name": "自动序号测试1", "matrial": "材料1", "unit": "个"},
            {"batno": "AUTO002", "name": "自动序号测试2", "matrial": "材料2", "unit": "套"},
            {"batno": "AUTO003", "name": "自动序号测试3", "matrial": "材料3", "unit": "件"},
            {"batno": "AUTO004", "name": "自动序号测试4", "matrial": "材料4", "unit": "台"},
            {"batno": "AUTO005", "name": "自动序号测试5", "matrial": "材料5", "unit": "副"}
        ],
        "name": "{{auto_index}} 占位符测试客户",
        "creator": "占位符测试员"
    },
    "template_name": "delivery"  # 使用原始模板（现在包含 {{auto_index}}）
}

print("🎯 测试 {{auto_index}} 占位符功能")
print("=" * 50)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 测试内容:")
    print("- 模板: delivery.xlsx")
    print("- 第1列: {{auto_index}} 占位符")
    print("- 数据: 5行测试数据")
    
    print("\n🎨 预期显示效果:")
    print("序号 | 批号    | 名称           | 材质  | 单位")
    print("-" * 50)
    print(" 1   | AUTO001 | 自动序号测试1  | 材料1 | 个")
    print(" 2   | AUTO002 | 自动序号测试2  | 材料2 | 套")
    print(" 3   | AUTO003 | 自动序号测试3  | 材料3 | 件")
    print(" 4   | AUTO004 | 自动序号测试4  | 材料4 | 台")
    print(" 5   | AUTO005 | 自动序号测试5  | 材料5 | 副")
    
    print("\n✨ {{auto_index}} 占位符特点:")
    print("🔢 明确指定: 在模板中明确放置 {{auto_index}}")
    print("🎯 灵活位置: 可以放在任意列，不限于第1列")
    print("🎨 样式继承: 继承模板单元格的所有样式")
    print("🔄 自动递增: 从1开始自动递增")
    print("📋 多个支持: 支持在多个列中使用相同序号")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 50)
print("🎊 {{auto_index}} 占位符测试完成！")
