import requests
import re

# 发送测试数据
data = {
    "data": {
        "list": [
            {"batno": "TEST001", "name": "测试商品1", "matrial": "塑料", "unit": "个"},
            {"batno": "TEST002", "name": "测试商品2", "matrial": "金属", "unit": "套"}
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 发送测试数据 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 获取显示页面的HTML
    display_response = requests.get("http://localhost:5000/display")
    html_content = display_response.text
    
    print("\n=== 检查HTML中的字体颜色 ===")
    
    # 查找表格中的样式
    table_pattern = r'<table[^>]*>(.*?)</table>'
    table_match = re.search(table_pattern, html_content, re.DOTALL)
    
    if table_match:
        table_html = table_match.group(1)
        
        # 查找所有包含color样式的单元格
        color_pattern = r'<t[dh][^>]*style="[^"]*color:\s*([^;"]+)[^"]*"[^>]*>([^<]*)</t[dh]>'
        color_matches = re.findall(color_pattern, table_html)
        
        print(f"找到{len(color_matches)}个设置了颜色的单元格:")
        for i, (color, content) in enumerate(color_matches[:10]):  # 只显示前10个
            print(f"  {i+1}. 颜色: {color}, 内容: '{content.strip()}'")
        
        # 查找所有单元格的样式
        cell_pattern = r'<t[dh][^>]*style="([^"]*)"[^>]*>([^<]*)</t[dh]>'
        cell_matches = re.findall(cell_pattern, table_html)
        
        print(f"\n=== 前5个单元格的完整样式 ===")
        for i, (style, content) in enumerate(cell_matches[:5]):
            print(f"单元格 {i+1}:")
            print(f"  内容: '{content.strip()}'")
            print(f"  样式: {style}")
            
            # 检查是否包含color属性
            if 'color:' in style:
                color_match = re.search(r'color:\s*([^;]+)', style)
                if color_match:
                    print(f"  ✅ 字体颜色: {color_match.group(1)}")
                else:
                    print(f"  ❌ 字体颜色解析失败")
            else:
                print(f"  ❌ 没有设置字体颜色")
            print()
        
        # 检查是否有数据行
        print("=== 检查数据行 ===")
        data_patterns = [
            r'TEST001',
            r'测试商品1',
            r'塑料',
            r'个'
        ]
        
        for pattern in data_patterns:
            matches = re.findall(rf'<t[dh][^>]*style="([^"]*)"[^>]*>{pattern}</t[dh]>', table_html)
            if matches:
                style = matches[0]
                print(f"'{pattern}' 的样式: {style}")
                if 'color:' in style:
                    color_match = re.search(r'color:\s*([^;]+)', style)
                    if color_match:
                        print(f"  ✅ 字体颜色: {color_match.group(1)}")
                    else:
                        print(f"  ❌ 字体颜色解析失败")
                else:
                    print(f"  ❌ 没有设置字体颜色")
            else:
                print(f"'{pattern}' 未找到")
    else:
        print("❌ 未找到表格")
        
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 测试完成 ===")
