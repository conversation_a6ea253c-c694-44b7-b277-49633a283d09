import sys
import os
sys.path.append('.')

try:
    from docx import Document
    from app import extract_paragraph_style, extract_cell_style
    
    print("🎨 简单样式测试")
    print("=" * 40)
    
    # 加载Word文档
    template_path = "templates/委外加工过程不良品记录表.docx"
    doc = Document(template_path)
    
    print(f"✅ Word文档加载成功: {template_path}")
    print(f"段落数量: {len(doc.paragraphs)}")
    print(f"表格数量: {len(doc.tables)}")
    
    # 测试段落样式提取
    print("\n📝 段落样式测试:")
    for i, para in enumerate(doc.paragraphs[:3]):  # 只测试前3个段落
        if para.text.strip():
            text = para.text.strip()[:20] + "..." if len(para.text.strip()) > 20 else para.text.strip()
            try:
                style = extract_paragraph_style(para)
                print(f"  段落{i+1}: {text}")
                print(f"    样式: {style}")
            except Exception as e:
                print(f"  段落{i+1}: 样式提取失败 - {e}")
    
    # 测试表格样式提取
    print("\n📊 表格样式测试:")
    if doc.tables:
        table = doc.tables[0]
        print(f"  表格1: {len(table.rows)}行 x {len(table.columns)}列")
        
        if table.rows:
            row = table.rows[0]
            for j, cell in enumerate(row.cells[:3]):  # 只测试前3个单元格
                try:
                    style = extract_cell_style(cell)
                    cell_text = cell.text.strip()[:15] + "..." if len(cell.text.strip()) > 15 else cell.text.strip()
                    print(f"    单元格{j+1}: {cell_text}")
                    print(f"      样式: {style}")
                except Exception as e:
                    print(f"    单元格{j+1}: 样式提取失败 - {e}")
    
    print("\n✅ 样式提取功能测试完成")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 40)
print("🎨 简单样式测试结束")
