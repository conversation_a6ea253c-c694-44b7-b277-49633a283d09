# 📁 项目结构说明

## 🎯 项目概述
模板打印系统 - 支持Excel和Word模板的动态数据填充、样式保持和文件导出功能。

## 📂 目录结构

```
print_sys/
├── 📄 app.py                          # 主应用程序
├── 📄 requirements.txt                # Python依赖包
├── 📄 app.log                         # 应用日志文件
├── 📄 print_test_guide.html           # 打印测试指南
│
├── 📁 templates/                      # 模板文件目录
│   ├── 📄 README.txt                  # 模板说明文件
│   ├── 📊 delivery.xlsx               # Excel模板示例（销售出库单）
│   ├── 📄 委外加工过程不良品记录表.docx  # Word模板示例
│   ├── 🌐 index.html                  # 首页模板
│   ├── 🌐 display.html                # 显示页面模板
│   └── 🌐 print_only.html             # 专用打印页面模板
│
├── 📁 static/                         # 静态资源目录
│   └── 🎨 style.css                   # 样式文件
│
├── 📁 filled_outputs/                 # 填充后文件输出目录
│   ├── 📊 filled_delivery.xlsx_*      # 填充后的Excel文件
│   └── 📄 filled_委外加工*.docx_*      # 填充后的Word文件
│
├── 📁 html_templates/                 # HTML模板缓存目录
│
├── 📁 __pycache__/                    # Python缓存目录
│
└── 📁 文档/                           # 功能说明文档
    ├── 📄 AUTO_INDEX_COMPLETE_GUIDE.md      # 自增序号功能完整指南
    ├── 📄 AUTO_INDEX_FEATURE.md             # 自增序号功能说明
    ├── 📄 SUM_FORMULA_GUIDE.md              # SUM公式功能指南
    ├── 📄 SUM_PLACEHOLDER_COMPLETE_GUIDE.md # SUM占位符完整指南
    ├── 📄 dynamic_list_guide.md             # 动态列表功能指南
    └── 📄 list_format_guide.md              # 列表格式指南
```

## 🔧 核心文件说明

### 📄 app.py
- **主应用程序**：Flask Web应用
- **核心功能**：
  - Excel模板处理 (`process_excel_template`)
  - Word模板处理 (`process_word_template`)
  - HTML转换和样式保持
  - 文件导出功能
  - API路由定义

### 📊 templates/delivery.xlsx
- **Excel模板示例**：销售出库单
- **支持功能**：
  - 动态列表：`{{list.field}}`
  - 自增序号：`{{auto_index}}`
  - SUM占位符：`{{sum.field}}`
  - 普通占位符：`{{field_name}}`

### 📄 templates/委外加工过程不良品记录表.docx
- **Word模板示例**：委外加工记录表
- **支持功能**：
  - 动态列表处理
  - 字体样式保持
  - 文本对齐保持
  - 表格结构保持

## 🌐 Web页面

### 🏠 首页 (/)
- 系统使用说明书
- 功能介绍和使用指南
- API文档和示例

### 📋 显示页面 (/display)
- 显示最新处理的模板结果
- 支持打印功能
- **导出功能**：一键导出Excel/Word文件

### 🖨️ 打印页面 (/print)
- 专用打印页面
- 纯白背景，适合打印
- 移除所有非打印元素

## 🔌 API端点

### POST /api/print
- **功能**：处理模板数据
- **输入**：JSON格式的模板数据
- **输出**：处理结果和HTML预览

### GET /api/export/info
- **功能**：获取可导出文件信息
- **输出**：文件类型、大小、修改时间等

### GET /api/export
- **功能**：下载填充后的文件
- **输出**：Excel或Word文件下载

## 📊 支持的功能

### ✅ Excel模板功能
- 📋 动态列表：`{{list.field}}`
- 🔢 自增序号：`{{auto_index}}`
- 🧮 SUM公式：`{{sum.field}}`
- 🎨 样式保持：字体、颜色、边框
- 📥 导出：Excel文件 (.xlsx)

### ✅ Word模板功能
- 📋 动态列表处理
- 🎨 字体样式：字体名称、大小、粗体
- 📐 文本对齐：居中、左对齐、右对齐
- 📄 布局保持：段落顺序、表格结构
- 📥 导出：Word文件 (.docx)

## 🚀 启动方式

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py

# 访问地址
http://localhost:5000
```

## 📝 使用流程

1. **准备模板**：在 `templates/` 目录放置Excel或Word模板
2. **发送数据**：通过API发送JSON数据到 `/api/print`
3. **查看结果**：访问 `/display` 页面查看填充结果
4. **导出文件**：点击"导出文件"按钮下载对应格式文件

## 🔧 技术栈

- **后端**：Python + Flask
- **Excel处理**：openpyxl
- **Word处理**：python-docx
- **前端**：HTML + CSS + JavaScript
- **样式**：Bootstrap + 自定义CSS

## 📋 注意事项

- 填充后的文件自动保存在 `filled_outputs/` 目录
- 文件名包含时间戳，避免覆盖
- 支持中文文件名和内容
- 自动识别模板类型并选择对应的处理方式
- 导出功能根据模板类型自动选择文件格式
