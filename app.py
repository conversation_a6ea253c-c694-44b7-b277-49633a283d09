from flask import Flask, request, render_template, jsonify, send_file
import os
import json
from openpyxl import load_workbook
from docx import Document
import logging
from datetime import datetime
from copy import copy

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 确保目录存在
os.makedirs('templates', exist_ok=True)
os.makedirs('filled_outputs', exist_ok=True)
os.makedirs('html_templates', exist_ok=True)

# 全局变量，存储最新的渲染数据
latest_render_data = None
# 全局变量，存储最新的输出文件路径
latest_output_file = None

@app.route('/', methods=['GET'])
def index():
    """首页，显示基本信息"""
    return render_template('index.html')

@app.route('/api/print', methods=['POST'])
def receive_print_request():
    """接收打印请求"""
    try:
        # 获取JSON数据
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        logger.info(f"Received print request: {json.dumps(data, ensure_ascii=False)}")

        # 提取模板名称和打印数据
        template_name = data.get('template_name')
        print_data = data.get('data')

        if not template_name:
            return jsonify({"error": "No template name provided"}), 400
        if not print_data:
            return jsonify({"error": "No print data provided"}), 400

        # 处理模板
        result = process_template(template_name, print_data)
        if "error" in result:
            return jsonify(result), 400

        # 返回成功响应
        return jsonify({
            "success": True,
            "message": "Print request processed successfully",
            "display_url": f"/display"
        })

    except Exception as e:
        logger.error(f"Error processing print request: {str(e)}")
        return jsonify({"error": str(e)}), 500

def process_template(template_name, data):
    """处理模板，填充数据"""
    try:
        # 查找模板文件
        template_path = find_template(template_name)
        if not template_path:
            return {"error": f"Template '{template_name}' not found"}

        # 根据文件扩展名选择处理方法
        ext = os.path.splitext(template_path)[1].lower()

        if ext == '.xlsx':
            return process_excel_template(template_path, data)
        elif ext == '.docx':
            return process_word_template(template_path, data)
        else:
            return {"error": f"Unsupported template format: {ext}"}

    except Exception as e:
        logger.error(f"Error processing template: {str(e)}")
        return {"error": str(e)}

def find_template(template_name):
    """查找模板文件"""
    # 在templates目录中查找模板
    for ext in ['.xlsx', '.docx']:
        path = os.path.join('templates', f"{template_name}{ext}")
        if os.path.exists(path):
            return path
    return None

def process_excel_template(template_path, data):
    """处理Excel模板"""
    try:
        # 加载Excel文件
        wb = load_workbook(template_path)
        ws = wb.active

        # 先处理动态列表（使用原始数据）
        process_dynamic_list_in_excel(ws, data)

        # 然后处理列表数据转换
        processed_data = process_list_data(data)

        # 遍历数据，填充到Excel中
        for key, value in processed_data.items():
            # 查找包含{{key}}的单元格
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str) and f"{{{{{key}}}}}" in cell.value:
                        cell.value = cell.value.replace(f"{{{{{key}}}}}", str(value))

        # 保存填充后的Excel
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_path = os.path.join('filled_outputs', f"filled_{os.path.basename(template_path)}_{timestamp}.xlsx")
        wb.save(output_path)

        # 将Excel数据转换为HTML表格
        html_table = excel_to_html_table(wb)

        # 存储渲染数据
        global latest_render_data, latest_output_file
        latest_render_data = {
            "title": f"Excel打印 - {os.path.basename(template_path)}",
            "content": html_table,
            "timestamp": timestamp
        }
        latest_output_file = output_path

        return {"success": True, "output_path": output_path}

    except Exception as e:
        logger.error(f"Error processing Excel template: {str(e)}")
        return {"error": str(e)}

def process_list_data(data):
    """处理列表数据，将列表转换为索引格式"""
    processed_data = data.copy()

    for key, value in data.items():
        if isinstance(value, list):
            # 将列表转换为索引格式
            for i, item in enumerate(value):
                if isinstance(item, dict):
                    # 如果列表项是字典，展开字典的键值对
                    for sub_key, sub_value in item.items():
                        processed_data[f"{key}{i+1}_{sub_key}"] = sub_value
                else:
                    # 如果列表项是简单值
                    processed_data[f"{key}{i+1}"] = item
            # 删除原始列表
            del processed_data[key]

    return processed_data

def process_dynamic_list_in_excel(ws, data):
    """在Excel中处理动态列表"""
    import re

    logger.info("开始处理动态列表")

    # 方法1: 查找包含{{list:}}标记的单元格
    list_markers = []
    for row_idx, row in enumerate(ws.iter_rows(), 1):
        for col_idx, cell in enumerate(row, 1):
            if cell.value and isinstance(cell.value, str) and "{{list:" in cell.value:
                # 提取列表名称
                match = re.search(r'\{\{list:(\w+)\}\}', cell.value)
                if match:
                    list_name = match.group(1)
                    list_markers.append({
                        'row': row_idx,
                        'col': col_idx,
                        'list_name': list_name,
                        'cell': cell
                    })
                    logger.info(f"找到{{list:}}标记: {cell.value} 在第{row_idx}行第{col_idx}列")

    # 方法2: 如果没有找到{{list:}}标记，查找{{list.field}}占位符
    if not list_markers:
        logger.info("未找到{{list:}}标记，查找{{list.field}}占位符")
        list_field_cells = []
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and isinstance(cell.value, str):
                    # 查找{{list.field}}格式
                    if re.search(r'\{\{list\.\w+\}\}', cell.value):
                        list_field_cells.append({
                            'row': row_idx,
                            'col': col_idx,
                            'cell': cell
                        })
                        logger.info(f"找到{{list.field}}占位符: {cell.value} 在第{row_idx}行第{col_idx}列")

        # 如果找到{{list.field}}占位符，自动创建列表标记
        if list_field_cells:
            # 找到第一个{{list.field}}所在的行，直接使用该行作为模板行
            first_row = min(cell['row'] for cell in list_field_cells)
            list_markers.append({
                'row': first_row,  # 直接使用包含{{list.field}}的行作为模板行
                'col': 1,
                'list_name': 'list',  # 默认使用'list'作为列表名
                'cell': None,  # 虚拟标记
                'auto_detected': True,
                'template_row': first_row  # 明确指定模板行
            })
            logger.info(f"自动创建列表标记，模板行: {first_row}")
        else:
            logger.info("未找到任何列表相关的占位符")

    # 处理每个列表标记
    for marker in list_markers:
        list_name = marker['list_name']
        if list_name in data and isinstance(data[list_name], list):
            # 获取列表数据
            list_data = data[list_name]
            start_row = marker['row']

            # 清除原始标记（如果不是自动检测的）
            if marker['cell'] is not None:
                marker['cell'].value = None

            # 查找列模板行
            if marker.get('auto_detected', False):
                # 自动检测模式：模板行就是包含{{list.field}}占位符的行
                template_row = marker.get('template_row', marker['row'])
                logger.info(f"自动检测模式：模板行为第{template_row}行")
            else:
                # 标准模式：模板行在标记行的下一行
                template_row = start_row + 1

            column_templates = {}
            max_col = 0

            # 扫描模板行，查找列占位符
            for col_idx in range(1, ws.max_column + 1):
                template_cell = ws.cell(row=template_row, column=col_idx)
                if template_cell.value and isinstance(template_cell.value, str):
                    # 查找形如 {{item.field}}、{{list.field}} 或 {{auto_index}} 的占位符
                    import re
                    # 支持四种格式：{{item.field}}、{{list.field}}、{{auto_index}} 和 {{sum.field}}
                    item_matches = re.findall(r'\{\{item\.(\w+)\}\}', template_cell.value)
                    list_matches = re.findall(r'\{\{list\.(\w+)\}\}', template_cell.value)
                    auto_index_matches = re.findall(r'\{\{auto_index\}\}', template_cell.value)
                    sum_matches = re.findall(r'\{\{sum\.(\w+)\}\}', template_cell.value)

                    if item_matches:
                        column_templates[col_idx] = {
                            'field': item_matches[0],
                            'template': template_cell.value,
                            'style': extract_cell_style(template_cell)
                        }
                        max_col = max(max_col, col_idx)
                        logger.info(f"检测到item字段: 第{col_idx}列 -> {item_matches[0]}")
                    elif list_matches:
                        column_templates[col_idx] = {
                            'field': list_matches[0],
                            'template': template_cell.value,
                            'style': extract_cell_style(template_cell)
                        }
                        max_col = max(max_col, col_idx)
                        logger.info(f"检测到list字段: 第{col_idx}列 -> {list_matches[0]}")
                    elif auto_index_matches:
                        column_templates[col_idx] = {
                            'field': 'auto_index',
                            'template': template_cell.value,
                            'style': extract_cell_style(template_cell)
                        }
                        max_col = max(max_col, col_idx)
                        logger.info(f"检测到自增序号占位符: 第{col_idx}列 -> {{{{auto_index}}}}")
                    elif sum_matches:
                        column_templates[col_idx] = {
                            'field': f'sum.{sum_matches[0]}',
                            'template': template_cell.value,
                            'style': extract_cell_style(template_cell)
                        }
                        max_col = max(max_col, col_idx)
                        logger.info(f"检测到SUM占位符: 第{col_idx}列 -> {{{{sum.{sum_matches[0]}}}}}")
                elif col_idx == 1 and (not template_cell.value or str(template_cell.value).strip() == ''):
                    # 第1列为空，自动设置为自增序号列（向后兼容）
                    column_templates[col_idx] = {
                        'field': 'auto_index',
                        'template': '{{auto_index}}',
                        'style': extract_cell_style(template_cell)
                    }
                    max_col = max(max_col, col_idx)
                    logger.info(f"检测到空的序号列: 第{col_idx}列 -> 自动添加自增序号（向后兼容）")

            # 如果没有找到列模板，使用简单模式
            if not column_templates:
                for i, item in enumerate(list_data):
                    current_row = start_row + i
                    if isinstance(item, dict):
                        col_offset = 0
                        for key, value in item.items():
                            ws.cell(row=current_row, column=marker['col'] + col_offset, value=value)
                            col_offset += 1
                    else:
                        ws.cell(row=current_row, column=marker['col'], value=item)
            else:
                # 使用列模板模式
                if marker.get('auto_detected', False):
                    # 自动检测模式：简单直接的方法
                    logger.info(f"自动检测模式：需要处理{len(list_data)}条数据")

                    # 先填充第一条数据到模板行
                    if list_data:
                        first_item = list_data[0]
                        logger.info(f"填充第1条数据到第{template_row}行: {first_item}")

                        if isinstance(first_item, dict):
                            for col_idx, template_info in column_templates.items():
                                field = template_info['field']

                                # 处理自增序号
                                if field == 'auto_index':
                                    # 检查是否是纯 {{auto_index}} 还是嵌入在文本中
                                    template_text = template_info.get('template', '{{auto_index}}')
                                    if template_text == '{{auto_index}}':
                                        # 纯序号
                                        value = 1
                                    else:
                                        # 嵌入在文本中，替换占位符
                                        value = template_text.replace('{{auto_index}}', '1')
                                    logger.info(f"  设置第{template_row}行第{col_idx}列 自增序号: {value}")
                                # 处理SUM占位符
                                elif field.startswith('sum.'):
                                    sum_field = field[4:]  # 移除 'sum.' 前缀
                                    sum_value = calculate_sum_for_field(list_data, sum_field)

                                    # 检查是否是纯 {{sum.field}} 还是嵌入在文本中
                                    template_text = template_info.get('template', f'{{{{sum.{sum_field}}}}}')
                                    if template_text == f'{{{{sum.{sum_field}}}}}':
                                        # 纯SUM值
                                        value = sum_value
                                    else:
                                        # 嵌入在文本中，替换占位符
                                        value = template_text.replace(f'{{{{sum.{sum_field}}}}}', str(sum_value))
                                    logger.info(f"  设置第{template_row}行第{col_idx}列 SUM({sum_field}): {value}")
                                else:
                                    value = first_item.get(field, '') if first_item.get(field) is not None else ''
                                    logger.info(f"  设置第{template_row}行第{col_idx}列 {field}: '{value}'")

                                # 设置单元格值，并保持原有样式
                                cell = ws.cell(row=template_row, column=col_idx)

                                # 保存原始样式
                                original_font = copy(cell.font) if cell.font else None
                                original_fill = copy(cell.fill) if cell.fill else None
                                original_border = copy(cell.border) if cell.border else None
                                original_alignment = copy(cell.alignment) if cell.alignment else None

                                # 设置值
                                cell.value = value

                                # 重新应用原始样式（确保样式不丢失）
                                try:
                                    if original_font:
                                        cell.font = original_font
                                    if original_fill:
                                        cell.fill = original_fill
                                    if original_border:
                                        cell.border = original_border
                                    if original_alignment:
                                        cell.alignment = original_alignment
                                    logger.info(f"    保持原始样式到第{template_row}行第{col_idx}列")
                                except Exception as e:
                                    logger.warning(f"    保持原始样式失败: {e}")

                    # 如果有多条数据，在模板行后面插入新行
                    if len(list_data) > 1:
                        logger.info(f"需要为剩余{len(list_data) - 1}条数据插入新行")

                        # 先处理合并单元格，避免冲突
                        merged_ranges_to_remove = []
                        merged_ranges_to_restore = []

                        for merged_range in list(ws.merged_cells.ranges):
                            # 检查合并单元格是否会影响我们要插入的行
                            if merged_range.min_row > template_row:
                                merged_ranges_to_remove.append(merged_range)
                                # 记录需要恢复的合并单元格信息
                                merged_ranges_to_restore.append({
                                    'min_row': merged_range.min_row,
                                    'max_row': merged_range.max_row,
                                    'min_col': merged_range.min_col,
                                    'max_col': merged_range.max_col
                                })

                        # 移除会冲突的合并单元格
                        for merged_range in merged_ranges_to_remove:
                            logger.info(f"移除合并单元格: {merged_range}")
                            ws.unmerge_cells(str(merged_range))

                        for i in range(1, len(list_data)):
                            item = list_data[i]
                            insert_row = template_row + i

                            # 在指定位置插入新行
                            ws.insert_rows(insert_row)
                            logger.info(f"在第{insert_row}行插入新行")

                            # 复制模板行的样式到新行
                            for col_idx in range(1, ws.max_column + 1):
                                template_cell = ws.cell(row=template_row, column=col_idx)
                                new_cell = ws.cell(row=insert_row, column=col_idx)

                                # 复制样式
                                try:
                                    if template_cell.font:
                                        new_cell.font = copy(template_cell.font)
                                    if template_cell.fill:
                                        new_cell.fill = copy(template_cell.fill)
                                    if template_cell.border:
                                        new_cell.border = copy(template_cell.border)
                                    if template_cell.alignment:
                                        new_cell.alignment = copy(template_cell.alignment)
                                except Exception as e:
                                    logger.warning(f"样式复制失败: {e}")

                            # 填充数据到新行
                            logger.info(f"填充第{i+1}条数据到第{insert_row}行: {item}")

                            if isinstance(item, dict):
                                for col_idx, template_info in column_templates.items():
                                    field = template_info['field']

                                    # 处理自增序号
                                    if field == 'auto_index':
                                        # 检查是否是纯 {{auto_index}} 还是嵌入在文本中
                                        template_text = template_info.get('template', '{{auto_index}}')
                                        if template_text == '{{auto_index}}':
                                            # 纯序号
                                            value = i + 1
                                        else:
                                            # 嵌入在文本中，替换占位符
                                            value = template_text.replace('{{auto_index}}', str(i + 1))
                                        logger.info(f"  设置第{insert_row}行第{col_idx}列 自增序号: {value}")
                                    # 处理SUM占位符（在数据行中跳过，SUM只在合计行显示）
                                    elif field.startswith('sum.'):
                                        # 在数据行中，SUM占位符显示为空
                                        value = ''
                                        logger.info(f"  设置第{insert_row}行第{col_idx}列 SUM占位符(数据行): 空值")
                                    else:
                                        value = item.get(field, '') if item.get(field) is not None else ''
                                        logger.info(f"  设置第{insert_row}行第{col_idx}列 {field}: '{value}'")

                                    # 设置单元格值，并保持原有样式
                                    cell = ws.cell(row=insert_row, column=col_idx)
                                    template_cell = ws.cell(row=template_row, column=col_idx)

                                    # 先设置值
                                    cell.value = value

                                    # 重新应用模板单元格的样式（确保样式不丢失）
                                    try:
                                        if template_cell.font:
                                            cell.font = copy(template_cell.font)
                                        if template_cell.fill:
                                            cell.fill = copy(template_cell.fill)
                                        if template_cell.border:
                                            cell.border = copy(template_cell.border)
                                        if template_cell.alignment:
                                            cell.alignment = copy(template_cell.alignment)
                                        logger.info(f"    重新应用样式到第{insert_row}行第{col_idx}列")
                                    except Exception as e:
                                        logger.warning(f"    重新应用样式失败: {e}")

                        # 恢复合并单元格（调整行号）
                        rows_added = len(list_data) - 1
                        for merge_info in merged_ranges_to_restore:
                            try:
                                # 调整行号：原来的行号 + 插入的行数
                                new_min_row = merge_info['min_row'] + rows_added
                                new_max_row = merge_info['max_row'] + rows_added

                                # 重新合并单元格
                                ws.merge_cells(
                                    start_row=new_min_row,
                                    start_column=merge_info['min_col'],
                                    end_row=new_max_row,
                                    end_column=merge_info['max_col']
                                )
                                logger.info(f"恢复合并单元格: 行{new_min_row}-{new_max_row}, 列{merge_info['min_col']}-{merge_info['max_col']}")
                            except Exception as e:
                                logger.warning(f"恢复合并单元格失败: {e}")

                        # 更新SUM公式
                        update_sum_formulas(ws, template_row, len(list_data))
                        logger.info("SUM公式更新完成")

                        # 处理SUM占位符（在所有行中扫描）
                        process_sum_placeholders(ws, list_data)
                        logger.info("SUM占位符处理完成")

                    logger.info("自动检测模式数据填充完成")
                else:
                    # 标准模式：先删除模板行，然后插入数据
                    ws.delete_rows(template_row)

                    # 插入数据行
                    for i, item in enumerate(list_data):
                        current_row = start_row + i
                        if isinstance(item, dict):
                            for col_idx, template_info in column_templates.items():
                                field = template_info['field']

                                # 处理自增序号
                                if field == 'auto_index':
                                    # 检查是否是纯 {{auto_index}} 还是嵌入在文本中
                                    template_text = template_info.get('template', '{{auto_index}}')
                                    if template_text == '{{auto_index}}':
                                        # 纯序号
                                        value = i + 1
                                    else:
                                        # 嵌入在文本中，替换占位符
                                        value = template_text.replace('{{auto_index}}', str(i + 1))
                                    logger.info(f"  设置第{current_row}行第{col_idx}列 自增序号: {value}")
                                else:
                                    value = item.get(field, '') if item.get(field) is not None else ''

                                # 创建新单元格
                                new_cell = ws.cell(row=current_row, column=col_idx, value=value)

                                # 应用样式
                                if template_info['style']:
                                    apply_style_to_cell(new_cell, template_info['style'])

                    # 更新SUM公式
                    update_sum_formulas(ws, start_row, len(list_data))
                    logger.info("SUM公式更新完成")

                    # 处理SUM占位符（在所有行中扫描）
                    process_sum_placeholders(ws, list_data)
                    logger.info("SUM占位符处理完成")

                    # 调整后续内容的位置（向下移动）
                    rows_added = len(list_data) - 1  # 减去删除的模板行
                    if rows_added > 0:
                        shift_rows_down(ws, template_row, rows_added, data)

                    logger.info("标准模式数据填充完成")

def apply_style_to_cell(cell, style_dict):
    """将样式应用到单元格"""
    from openpyxl.styles import Font, Alignment, PatternFill

    # 应用字体样式
    if 'font-family' in style_dict or 'font-size' in style_dict or 'font-weight' in style_dict or 'color' in style_dict:
        font_kwargs = {}
        if 'font-family' in style_dict:
            font_kwargs['name'] = style_dict['font-family']
        if 'font-size' in style_dict:
            size_str = style_dict['font-size'].replace('pt', '')
            font_kwargs['size'] = float(size_str)
        if 'font-weight' in style_dict and style_dict['font-weight'] == 'bold':
            font_kwargs['bold'] = True
        if 'color' in style_dict:
            color = style_dict['color'].replace('#', '')
            font_kwargs['color'] = color

        if font_kwargs:
            cell.font = Font(**font_kwargs)

    # 应用对齐样式
    if 'text-align' in style_dict or 'vertical-align' in style_dict:
        align_kwargs = {}
        if 'text-align' in style_dict:
            # 映射HTML对齐方式到Excel对齐方式
            html_to_excel_horizontal = {
                'left': 'left',
                'center': 'center',
                'right': 'right',
                'justify': 'justify'
            }
            horizontal = style_dict['text-align']
            if horizontal in html_to_excel_horizontal:
                align_kwargs['horizontal'] = html_to_excel_horizontal[horizontal]

        if 'vertical-align' in style_dict:
            # 映射HTML垂直对齐到Excel垂直对齐
            html_to_excel_vertical = {
                'top': 'top',
                'middle': 'center',  # 注意：HTML的middle对应Excel的center
                'bottom': 'bottom'
            }
            vertical = style_dict['vertical-align']
            if vertical in html_to_excel_vertical:
                align_kwargs['vertical'] = html_to_excel_vertical[vertical]

        if align_kwargs:
            try:
                cell.alignment = Alignment(**align_kwargs)
            except Exception:
                # 对齐设置出错，跳过
                pass

def process_sum_placeholders(ws, list_data):
    """处理所有SUM占位符，在整个工作表中扫描并替换"""
    import re

    logger.info("开始处理SUM占位符")

    # 扫描所有单元格，查找SUM占位符
    for row in range(1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            if cell.value and isinstance(cell.value, str):
                # 查找SUM占位符
                sum_matches = re.findall(r'\{\{sum\.(\w+)\}\}', cell.value)
                if sum_matches:
                    original_value = cell.value
                    new_value = original_value

                    # 处理每个SUM占位符
                    for field_name in sum_matches:
                        sum_value = calculate_sum_for_field(list_data, field_name)
                        placeholder = f'{{{{sum.{field_name}}}}}'
                        new_value = new_value.replace(placeholder, str(sum_value))
                        logger.info(f"替换SUM占位符 {cell.coordinate}: {placeholder} -> {sum_value}")

                    # 更新单元格值
                    cell.value = new_value
                    logger.info(f"更新单元格 {cell.coordinate}: '{original_value}' -> '{new_value}'")

def calculate_sum_for_field(list_data, field_name):
    """计算指定字段的总和"""
    total = 0
    count = 0

    logger.info(f"开始计算字段 '{field_name}' 的总和")

    for item in list_data:
        if isinstance(item, dict) and field_name in item:
            value = item[field_name]
            try:
                # 尝试转换为数字
                if value is not None:
                    if isinstance(value, (int, float)):
                        total += value
                        count += 1
                        logger.info(f"  添加数值: {value}")
                    elif isinstance(value, str):
                        # 尝试转换字符串为数字
                        cleaned_value = value.replace(',', '').replace(' ', '')
                        if cleaned_value:
                            numeric_value = float(cleaned_value)
                            total += numeric_value
                            count += 1
                            logger.info(f"  添加字符串数值: '{value}' -> {numeric_value}")
            except (ValueError, TypeError) as e:
                logger.warning(f"  跳过非数值: '{value}' ({e})")
                continue

    logger.info(f"字段 '{field_name}' 求和完成: {count}个数值, 总和={total}")
    return total

def update_sum_formulas(ws, template_row, data_count):
    """更新SUM公式的范围"""
    import re

    logger.info(f"开始更新SUM公式，模板行: {template_row}, 数据行数: {data_count}")

    # 计算数据范围
    data_start_row = template_row
    data_end_row = template_row + data_count - 1

    logger.info(f"数据范围: 第{data_start_row}行到第{data_end_row}行")

    # 扫描所有单元格，查找SUM公式
    for row in range(1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
                formula = cell.value
                logger.info(f"发现公式 {cell.coordinate}: {formula}")

                # 检查是否是SUM公式
                if 'SUM(' in formula.upper():
                    # 更新SUM公式的范围
                    updated_formula = update_sum_range(formula, col, data_start_row, data_end_row)
                    if updated_formula != formula:
                        cell.value = updated_formula
                        logger.info(f"更新公式 {cell.coordinate}: {formula} -> {updated_formula}")

def update_sum_range(formula, column, start_row, end_row):
    """更新SUM公式中的范围"""
    import re
    from openpyxl.utils import get_column_letter

    # 获取列字母
    col_letter = get_column_letter(column)

    # 匹配SUM公式中的范围，例如 SUM(F5:F5) 或 SUM(F5:F10)
    pattern = r'SUM\(([A-Z]+)(\d+):([A-Z]+)(\d+)\)'

    def replace_range(match):
        start_col = match.group(1)
        start_row_old = int(match.group(2))
        end_col = match.group(3)
        end_row_old = int(match.group(4))

        # 如果列匹配，更新范围
        if start_col == col_letter and end_col == col_letter:
            return f'SUM({col_letter}{start_row}:{col_letter}{end_row})'
        else:
            # 保持原样
            return match.group(0)

    updated_formula = re.sub(pattern, replace_range, formula, flags=re.IGNORECASE)
    return updated_formula

def shift_rows_down(ws, start_row, rows_to_add, data):
    """将指定行之后的内容向下移动，并更新占位符"""
    # 这个函数用于处理动态列表后的内容位移
    # 由于openpyxl的限制，这里采用简化处理
    pass

def extract_cell_style(cell):
    """提取Excel单元格的样式信息"""
    style = {}

    # 字体样式
    if cell.font:
        if cell.font.name:
            style['font-family'] = cell.font.name
        if cell.font.size:
            style['font-size'] = f"{cell.font.size}pt"
        if cell.font.bold:
            style['font-weight'] = 'bold'
        if cell.font.italic:
            style['font-style'] = 'italic'
        if cell.font.color:
            # 处理颜色值
            try:
                color = cell.font.color

                # 处理RGB颜色
                if color.rgb and isinstance(color.rgb, str):
                    color_rgb = color.rgb
                    if len(color_rgb) == 8:
                        # 移除alpha通道，只保留RGB
                        color_rgb = color_rgb[2:]
                    elif len(color_rgb) == 6:
                        # 已经是RGB格式
                        pass
                    else:
                        color_rgb = None

                    if color_rgb and len(color_rgb) == 6:
                        style['color'] = f"#{color_rgb}"

                # 处理主题颜色
                elif hasattr(color, 'theme') and color.theme is not None:
                    # 主题颜色映射
                    theme_colors = {
                        0: '#FFFFFF',  # 白色
                        1: '#000000',  # 黑色
                        2: '#1F497D',  # 深蓝色
                        3: '#4F81BD',  # 蓝色
                        4: '#9CBB58',  # 绿色
                        5: '#8064A2',  # 紫色
                        6: '#F79646',  # 橙色
                        7: '#4BACC6',  # 青色
                        8: '#F2F2F2',  # 浅灰色
                        9: '#808080',  # 灰色
                    }

                    if color.theme in theme_colors:
                        style['color'] = theme_colors[color.theme]
                    else:
                        # 未知主题颜色，默认使用黑色
                        style['color'] = '#000000'

                # 处理自动颜色或其他情况
                else:
                    # 默认使用黑色
                    style['color'] = '#000000'

            except Exception:
                # 颜色处理出错，使用默认黑色
                style['color'] = '#000000'
        else:
            # 没有设置字体颜色，使用默认黑色
            style['color'] = '#000000'

    # 对齐方式
    if cell.alignment:
        if cell.alignment.horizontal:
            alignment_map = {
                'left': 'left',
                'center': 'center',
                'right': 'right',
                'justify': 'justify'
            }
            if cell.alignment.horizontal in alignment_map:
                style['text-align'] = alignment_map[cell.alignment.horizontal]

        if cell.alignment.vertical:
            vertical_map = {
                'top': 'top',
                'center': 'middle',
                'bottom': 'bottom'
            }
            if cell.alignment.vertical in vertical_map:
                style['vertical-align'] = vertical_map[cell.alignment.vertical]

    # 背景颜色 - 强制使用白色背景以确保打印效果
    # 注释掉原始背景色处理，强制使用白色
    # if cell.fill and cell.fill.start_color and cell.fill.start_color.rgb:
    #     bg_color = cell.fill.start_color.rgb
    #     if isinstance(bg_color, str) and len(bg_color) == 8:
    #         bg_color = bg_color[2:]  # 移除alpha通道
    #         if bg_color != 'FFFFFF':  # 不是白色才设置背景
    #             style['background-color'] = f"#{bg_color}"

    # 强制设置白色背景以确保打印效果
    style['background-color'] = '#FFFFFF'

    return style

def get_column_width(ws, col_idx):
    """获取列宽度（像素）"""
    col_letter = chr(65 + col_idx)  # A, B, C...
    if col_letter in ws.column_dimensions:
        width = ws.column_dimensions[col_letter].width
        if width:
            # Excel列宽单位转换为像素（大约）
            return int(width * 7)
    return None

def get_row_height(ws, row_idx):
    """获取行高度（像素）"""
    row_num = row_idx + 1
    if row_num in ws.row_dimensions:
        height = ws.row_dimensions[row_num].height
        if height:
            # Excel行高单位转换为像素（大约）
            return int(height * 1.33)
    return None

def excel_to_html_table(workbook):
    """将Excel工作簿转换为HTML表格，支持合并单元格和样式"""
    ws = workbook.active

    # 获取工作表的最大行数和列数
    max_row = ws.max_row
    max_col = ws.max_column

    # 创建一个二维数组来存储单元格信息
    cell_matrix = []
    for r in range(max_row):
        row = []
        for c in range(max_col):
            row.append({
                'value': '',
                'colspan': 1,
                'rowspan': 1,
                'skip': False,
                'style': {}
            })
        cell_matrix.append(row)

    # 填充单元格数据和样式
    for row_idx, row in enumerate(ws.iter_rows()):
        for col_idx, cell in enumerate(row):
            if cell.value is not None:
                cell_matrix[row_idx][col_idx]['value'] = str(cell.value)

            # 提取单元格样式
            cell_style = extract_cell_style(cell)
            cell_matrix[row_idx][col_idx]['style'] = cell_style

    # 处理合并单元格
    for merged_range in ws.merged_cells.ranges:
        min_row = merged_range.min_row - 1  # 转换为0基索引
        max_row_range = merged_range.max_row - 1
        min_col = merged_range.min_col - 1
        max_col_range = merged_range.max_col - 1

        # 设置合并单元格的跨度
        cell_matrix[min_row][min_col]['colspan'] = max_col_range - min_col + 1
        cell_matrix[min_row][min_col]['rowspan'] = max_row_range - min_row + 1

        # 标记其他被合并的单元格为跳过
        for r in range(min_row, max_row_range + 1):
            for c in range(min_col, max_col_range + 1):
                if r != min_row or c != min_col:
                    cell_matrix[r][c]['skip'] = True

    # 生成HTML表格
    html = '<table class="table table-striped table-bordered">'

    # 添加列组定义（设置列宽）
    html += '<colgroup>'
    for col_idx in range(max_col):
        width = get_column_width(ws, col_idx)
        if width:
            html += f'<col style="width: {width}px;">'
        else:
            html += '<col>'
    html += '</colgroup>'

    # 检查是否有表头（第一行是否包含文本）
    has_header = any(cell_matrix[0][c]['value'].strip() for c in range(max_col) if not cell_matrix[0][c]['skip'])

    if has_header:
        html += '<thead>'
        html += generate_html_row(cell_matrix[0], True, ws, 0)
        html += '</thead>'
        start_row = 1
    else:
        start_row = 0

    # 添加表格内容
    html += '<tbody>'
    for row_idx in range(start_row, max_row):
        html += generate_html_row(cell_matrix[row_idx], False, ws, row_idx)
    html += '</tbody>'

    html += '</table>'
    return html

def generate_html_row(row_data, is_header=False, ws=None, row_idx=None):
    """生成HTML表格行"""
    tag = 'th' if is_header else 'td'

    # 获取行高
    row_style = ''
    if ws and row_idx is not None:
        height = get_row_height(ws, row_idx)
        if height:
            row_style = f' style="height: {height}px;"'

    html = f'<tr{row_style}>'

    for cell_data in row_data:
        if cell_data['skip']:
            continue

        # 构建单元格属性
        attrs = []
        if cell_data['colspan'] > 1:
            attrs.append(f'colspan="{cell_data["colspan"]}"')
        if cell_data['rowspan'] > 1:
            attrs.append(f'rowspan="{cell_data["rowspan"]}"')

        # 构建样式字符串
        style_parts = []
        for prop, value in cell_data['style'].items():
            style_parts.append(f'{prop}: {value}')

        if style_parts:
            attrs.append(f'style="{"; ".join(style_parts)}"')

        attr_str = ' ' + ' '.join(attrs) if attrs else ''

        # 处理空值
        value = cell_data['value'] if cell_data['value'] else '&nbsp;'

        html += f'<{tag}{attr_str}>{value}</{tag}>'

    html += '</tr>'
    return html

def process_word_template(template_path, data):
    """处理Word模板，支持动态列表、自增序号和SUM占位符"""
    try:
        # 加载Word文件
        doc = Document(template_path)

        logger.info(f"开始处理Word模板: {template_path}")
        logger.info(f"数据内容: {data}")

        # 处理动态列表数据
        list_data = data.get('list', [])
        if list_data:
            logger.info(f"检测到动态列表数据，共{len(list_data)}项")
            process_word_dynamic_lists(doc, list_data, data)

        # 处理普通占位符
        process_word_placeholders(doc, data)

        # 处理SUM占位符
        if list_data:
            process_word_sum_placeholders(doc, list_data)

        # 保存填充后的Word
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_path = os.path.join('filled_outputs', f"filled_{os.path.basename(template_path)}_{timestamp}.docx")
        doc.save(output_path)
        logger.info(f"Word文件已保存: {output_path}")

        # 提取Word内容为HTML
        html_content = convert_word_to_html(doc)

        # 存储渲染数据
        global latest_render_data, latest_output_file
        latest_render_data = {
            "title": f"Word打印 - {os.path.basename(template_path)}",
            "content": html_content,
            "timestamp": timestamp
        }
        latest_output_file = output_path

        return {"success": True, "output_path": output_path}

    except Exception as e:
        logger.error(f"Error processing Word template: {str(e)}")
        return {"error": str(e)}

def process_word_placeholders(doc, data):
    """处理Word文档中的普通占位符"""
    import re

    logger.info("开始处理Word普通占位符")

    # 处理段落中的占位符
    for paragraph in doc.paragraphs:
        if paragraph.text:
            original_text = paragraph.text
            new_text = original_text

            # 查找并替换占位符
            for key, value in data.items():
                if key != 'list':  # 跳过list数据，单独处理
                    placeholder = f"{{{{{key}}}}}"
                    if placeholder in new_text:
                        new_text = new_text.replace(placeholder, str(value))
                        logger.info(f"段落占位符替换: {placeholder} -> {value}")

            if new_text != original_text:
                paragraph.text = new_text

    # 处理表格中的占位符
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                if cell.text:
                    original_text = cell.text
                    new_text = original_text

                    # 查找并替换占位符
                    for key, value in data.items():
                        if key != 'list':  # 跳过list数据，单独处理
                            placeholder = f"{{{{{key}}}}}"
                            if placeholder in new_text:
                                new_text = new_text.replace(placeholder, str(value))
                                logger.info(f"表格占位符替换: {placeholder} -> {value}")

                    if new_text != original_text:
                        cell.text = new_text

def process_word_dynamic_lists(doc, list_data, data):
    """处理Word文档中的动态列表"""
    import re

    logger.info("开始处理Word动态列表")

    # 查找包含list占位符的表格
    for table in doc.tables:
        template_row_index = None
        list_placeholders = {}

        # 查找模板行
        for row_index, row in enumerate(table.rows):
            for cell_index, cell in enumerate(row.cells):
                if cell.text:
                    # 查找list占位符
                    list_matches = re.findall(r'\{\{list\.(\w+)\}\}', cell.text)
                    auto_index_matches = re.findall(r'\{\{auto_index\}\}', cell.text)

                    if list_matches or auto_index_matches:
                        if template_row_index is None:
                            template_row_index = row_index
                            logger.info(f"找到模板行: 第{row_index + 1}行")

                        if list_matches:
                            list_placeholders[cell_index] = {
                                'type': 'list',
                                'field': list_matches[0],
                                'template': cell.text
                            }
                        elif auto_index_matches:
                            list_placeholders[cell_index] = {
                                'type': 'auto_index',
                                'template': cell.text
                            }

        # 如果找到了模板行，处理动态列表
        if template_row_index is not None and list_placeholders:
            logger.info(f"处理动态列表，模板行: {template_row_index + 1}, 数据行数: {len(list_data)}")
            process_word_table_list(table, template_row_index, list_placeholders, list_data)

def process_word_table_list(table, template_row_index, list_placeholders, list_data):
    """处理Word表格中的动态列表"""

    # 获取模板行
    template_row = table.rows[template_row_index]

    # 为每个数据项创建新行
    for i, item in enumerate(list_data):
        if i == 0:
            # 第一行使用模板行
            current_row = template_row
        else:
            # 后续行需要插入新行
            current_row = table.add_row()

        # 填充数据
        for cell_index, placeholder_info in list_placeholders.items():
            if cell_index < len(current_row.cells):
                cell = current_row.cells[cell_index]

                if placeholder_info['type'] == 'auto_index':
                    # 处理自增序号
                    cell.text = str(i + 1)
                    logger.info(f"设置自增序号: 第{i + 1}行 -> {i + 1}")
                elif placeholder_info['type'] == 'list':
                    # 处理list字段
                    field = placeholder_info['field']
                    value = item.get(field, '') if isinstance(item, dict) else ''
                    cell.text = str(value)
                    logger.info(f"设置list字段: 第{i + 1}行 {field} -> {value}")

def process_word_sum_placeholders(doc, list_data):
    """处理Word文档中的SUM占位符"""
    import re

    logger.info("开始处理Word SUM占位符")

    # 处理段落中的SUM占位符
    for paragraph in doc.paragraphs:
        if paragraph.text:
            original_text = paragraph.text
            new_text = original_text

            # 查找SUM占位符
            sum_matches = re.findall(r'\{\{sum\.(\w+)\}\}', original_text)
            for field_name in sum_matches:
                sum_value = calculate_sum_for_field(list_data, field_name)
                placeholder = f'{{{{sum.{field_name}}}}}'
                new_text = new_text.replace(placeholder, str(sum_value))
                logger.info(f"段落SUM占位符替换: {placeholder} -> {sum_value}")

            if new_text != original_text:
                paragraph.text = new_text

    # 处理表格中的SUM占位符
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                if cell.text:
                    original_text = cell.text
                    new_text = original_text

                    # 查找SUM占位符
                    sum_matches = re.findall(r'\{\{sum\.(\w+)\}\}', original_text)
                    for field_name in sum_matches:
                        sum_value = calculate_sum_for_field(list_data, field_name)
                        placeholder = f'{{{{sum.{field_name}}}}}'
                        new_text = new_text.replace(placeholder, str(sum_value))
                        logger.info(f"表格SUM占位符替换: {placeholder} -> {sum_value}")

                    if new_text != original_text:
                        cell.text = new_text

def extract_paragraph_style(para):
    """提取段落样式并转换为CSS"""
    styles = []

    # 基础样式
    styles.append("margin: 8px 0")
    styles.append("line-height: 1.5")

    # 检查段落对齐方式
    if para.alignment is not None:
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        if para.alignment == WD_ALIGN_PARAGRAPH.CENTER:
            styles.append("text-align: center")
        elif para.alignment == WD_ALIGN_PARAGRAPH.RIGHT:
            styles.append("text-align: right")
        elif para.alignment == WD_ALIGN_PARAGRAPH.JUSTIFY:
            styles.append("text-align: justify")
        else:
            styles.append("text-align: left")
    else:
        styles.append("text-align: left")

    # 检查段落中的字体样式（取第一个run的样式）
    if para.runs:
        first_run = para.runs[0]
        run_styles = extract_run_style(first_run)
        styles.extend(run_styles)

    return "; ".join(styles)

def extract_run_style(run):
    """提取文本运行样式"""
    styles = []

    # 字体名称
    if run.font.name:
        styles.append(f"font-family: '{run.font.name}', SimSun, serif")
    else:
        styles.append("font-family: SimSun, serif")

    # 字体大小
    if run.font.size:
        # Word字体大小单位是Twips (1/20 point)，转换为pt
        size_pt = run.font.size.pt
        styles.append(f"font-size: {size_pt}pt")
    else:
        styles.append("font-size: 12pt")

    # 字体粗细
    if run.font.bold:
        styles.append("font-weight: bold")

    # 字体斜体
    if run.font.italic:
        styles.append("font-style: italic")

    # 下划线
    if run.font.underline:
        styles.append("text-decoration: underline")

    # 字体颜色
    if run.font.color and run.font.color.rgb:
        color = run.font.color.rgb
        styles.append(f"color: rgb({color.red}, {color.green}, {color.blue})")

    return styles

def extract_word_cell_style(cell):
    """提取Word表格单元格样式"""
    styles = []

    # 基础边框和内边距
    styles.append("border: 1px solid #000")
    styles.append("padding: 4px 8px")
    styles.append("vertical-align: top")

    # 检查单元格对齐方式
    if cell.paragraphs:
        first_para = cell.paragraphs[0]
        if first_para.alignment is not None:
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            if first_para.alignment == WD_ALIGN_PARAGRAPH.CENTER:
                styles.append("text-align: center")
            elif first_para.alignment == WD_ALIGN_PARAGRAPH.RIGHT:
                styles.append("text-align: right")
            elif first_para.alignment == WD_ALIGN_PARAGRAPH.JUSTIFY:
                styles.append("text-align: justify")
            else:
                styles.append("text-align: left")
        else:
            styles.append("text-align: left")

        # 检查单元格中的字体样式
        if first_para.runs:
            first_run = first_para.runs[0]
            run_styles = extract_run_style(first_run)
            styles.extend(run_styles)
    else:
        styles.append("text-align: left")
        styles.append("font-family: SimSun, serif")
        styles.append("font-size: 12pt")

    return "; ".join(styles)

def convert_word_to_html(doc):
    """将Word文档转换为HTML，保留字体、大小、居中等样式"""
    html_content = "<div class='word-content' style='font-family: SimSun, serif; background-color: white;'>"

    # 分析Word文档结构，找出哪些段落应该在表格前，哪些应该在表格后
    paragraphs = doc.paragraphs
    tables = doc.tables

    # 处理前置段落（标题和说明）
    for para in paragraphs:
        if para.text.strip():
            text = para.text.strip()
            # 如果是标题或说明性段落（不包含"签字"、"接收"等关键词）
            if not any(keyword in text for keyword in ["签字", "接收", "日期："]):
                para_style = extract_paragraph_style(para)
                html_content += f"<p style='{para_style}'>{text}</p>"

    # 处理所有表格
    for table in tables:
        table_style = "width: 100%; border-collapse: collapse; margin: 10px 0;"
        html_content += f"<table style='{table_style}'>"
        for row in table.rows:
            html_content += "<tr>"
            for cell in row.cells:
                cell_style = extract_word_cell_style(cell)
                cell_text = cell.text if cell.text else '&nbsp;'
                html_content += f"<td style='{cell_style}'>{cell_text}</td>"
            html_content += "</tr>"
        html_content += "</table>"

    # 处理后置段落（签字部分）
    html_content += "<div style='margin-top: 20px;'>"
    for para in paragraphs:
        if para.text.strip():
            text = para.text.strip()
            # 如果是签字相关段落
            if any(keyword in text for keyword in ["签字", "接收", "日期："]):
                para_style = extract_paragraph_style(para)
                html_content += f"<p style='{para_style}'>{text}</p>"
    html_content += "</div>"

    html_content += "</div>"
    return html_content

@app.route('/display', methods=['GET'])
def display():
    """显示最新渲染的内容"""
    if latest_render_data:
        return render_template('display.html',
                              title=latest_render_data["title"],
                              content=latest_render_data["content"],
                              timestamp=latest_render_data["timestamp"])
    else:
        return render_template('display.html',
                              title="无数据",
                              content="<p>当前没有渲染数据</p>",
                              timestamp="")

@app.route('/print', methods=['GET'])
def print_only():
    """专门的打印页面，强制白色背景"""
    if latest_render_data:
        return render_template('print_only.html',
                              title=latest_render_data["title"],
                              content=latest_render_data["content"],
                              timestamp=latest_render_data["timestamp"])
    else:
        return render_template('print_only.html',
                              title="无数据",
                              content="<p>当前没有渲染数据</p>",
                              timestamp="")

@app.route('/print-guide', methods=['GET'])
def print_guide():
    """显示打印指南"""
    try:
        with open('print_test_guide.html', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return "<h1>打印指南文件未找到</h1>", 404

@app.route('/api/templates', methods=['GET'])
def list_templates():
    """列出所有可用的模板"""
    try:
        templates = []
        template_dir = 'templates'

        if os.path.exists(template_dir):
            for filename in os.listdir(template_dir):
                if filename.endswith(('.xlsx', '.docx')):
                    template_name = os.path.splitext(filename)[0]
                    file_path = os.path.join(template_dir, filename)
                    file_size = os.path.getsize(file_path)
                    modified_time = os.path.getmtime(file_path)

                    templates.append({
                        "name": template_name,
                        "filename": filename,
                        "type": "Excel" if filename.endswith('.xlsx') else "Word",
                        "size": file_size,
                        "modified": datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M:%S")
                    })

        return jsonify({
            "success": True,
            "templates": templates,
            "count": len(templates)
        })

    except Exception as e:
        logger.error(f"Error listing templates: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/templates/<template_name>', methods=['DELETE'])
def delete_template(template_name):
    """删除指定的模板"""
    try:
        # 查找模板文件
        template_path = find_template(template_name)
        if not template_path:
            return jsonify({"error": f"Template '{template_name}' not found"}), 404

        # 删除文件
        os.remove(template_path)
        logger.info(f"Template '{template_name}' deleted successfully")

        return jsonify({
            "success": True,
            "message": f"Template '{template_name}' deleted successfully"
        })

    except Exception as e:
        logger.error(f"Error deleting template '{template_name}': {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/export')
def export_file():
    """导出文件API - 根据模板类型导出Excel或Word文件"""
    global latest_render_data, latest_output_file

    try:
        if not latest_output_file or not os.path.exists(latest_output_file):
            return jsonify({"error": "没有可导出的文件"}), 404

        # 获取文件扩展名来确定文件类型
        file_ext = os.path.splitext(latest_output_file)[1].lower()
        filename = os.path.basename(latest_output_file)

        # 设置正确的MIME类型
        if file_ext == '.xlsx':
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            file_type = 'Excel'
        elif file_ext == '.docx':
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            file_type = 'Word'
        else:
            mimetype = 'application/octet-stream'
            file_type = '未知'

        logger.info(f"导出{file_type}文件: {latest_output_file}, 类型: {mimetype}")

        return send_file(
            latest_output_file,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )

    except Exception as e:
        logger.error(f"文件导出失败: {str(e)}")
        return jsonify({"error": f"文件导出失败: {str(e)}"}), 500

@app.route('/api/export/info')
def export_info():
    """获取可导出文件的信息"""
    global latest_output_file, latest_render_data

    try:
        if not latest_output_file or not os.path.exists(latest_output_file):
            return jsonify({
                "available": False,
                "message": "没有可导出的文件"
            })

        # 获取文件信息
        file_ext = os.path.splitext(latest_output_file)[1].lower()
        filename = os.path.basename(latest_output_file)
        file_size = os.path.getsize(latest_output_file)
        modified_time = os.path.getmtime(latest_output_file)

        file_type = 'Excel' if file_ext == '.xlsx' else 'Word' if file_ext == '.docx' else '未知'

        return jsonify({
            "available": True,
            "filename": filename,
            "type": file_type,
            "extension": file_ext,
            "size": file_size,
            "size_mb": round(file_size / 1024 / 1024, 2),
            "modified": datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M:%S"),
            "title": latest_render_data.get('title', '未知') if latest_render_data else '未知'
        })

    except Exception as e:
        logger.error(f"获取导出信息失败: {str(e)}")
        return jsonify({"error": f"获取导出信息失败: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
