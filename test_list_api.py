import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 方法1：固定数量的列表项
print("=== 测试方法1：固定数量的列表项 ===")
data1 = {
    "template_name": "list_example",
    "data": {
        "order_id": "ORD001",
        "customer_name": "张三",
        "order_date": "2023-05-01",
        # 固定格式的商品列表
        "items1_name": "苹果",
        "items1_quantity": "10",
        "items1_price": "5.00",
        "items1_total": "50.00",
        "items2_name": "香蕉",
        "items2_quantity": "20",
        "items2_price": "3.00",
        "items2_total": "60.00",
        "items3_name": "橙子",
        "items3_quantity": "15",
        "items3_price": "4.00",
        "items3_total": "60.00",
        "total_amount": "170.00"
    }
}

response1 = requests.post(url, json=data1)
print(f"状态码: {response1.status_code}")
print(f"响应: {response1.text}")

print("\n" + "="*50 + "\n")

# 方法2：使用列表数据结构
print("=== 测试方法2：使用列表数据结构 ===")
data2 = {
    "template_name": "list_example",
    "data": {
        "order_id": "ORD002",
        "customer_name": "李四",
        "order_date": "2023-05-02",
        # 列表格式的商品数据
        "items": [
            {"name": "笔记本电脑", "quantity": "1", "price": "5000.00", "total": "5000.00"},
            {"name": "鼠标", "quantity": "2", "price": "50.00", "total": "100.00"},
            {"name": "键盘", "quantity": "1", "price": "200.00", "total": "200.00"}
        ],
        "total_amount": "5300.00"
    }
}

response2 = requests.post(url, json=data2)
print(f"状态码: {response2.status_code}")
print(f"响应: {response2.text}")

print("\n" + "="*50 + "\n")

# 方法3：简单列表
print("=== 测试方法3：简单列表 ===")
data3 = {
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-03",
        "company": "测试公司",
        "contact": "王五",
        "phone": "13800138000",
        # 使用列表，系统会自动转换为索引格式
        "products": ["产品A", "产品B", "产品C"],
        "total_amount": "1000.00",
        "remarks": "测试列表功能"
    }
}

response3 = requests.post(url, json=data3)
print(f"状态码: {response3.status_code}")
print(f"响应: {response3.text}")

if response3.status_code == 200:
    print("\n请访问 http://localhost:5000/display 查看打印结果")
