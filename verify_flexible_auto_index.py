import openpyxl
import glob
import os

# 找到最新生成的Excel文件
excel_files = glob.glob("filled_outputs/filled_flexible_auto_index.xlsx_*.xlsx")
if excel_files:
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"检查最新文件: {latest_file}")
    
    # 加载Excel文件
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 验证灵活 {{auto_index}} 功能 ===")
    
    # 检查数据行
    data_rows = [5, 6, 7]  # 数据行
    expected_indexes = [1, 2, 3]  # 期望的序号
    
    print("详细验证:")
    for i, row_num in enumerate(data_rows):
        print(f"\n第{row_num}行:")
        
        # 检查各列内容
        columns = ['序号', '编号', '商品名称', '规格', '单位', '备注']
        for col_idx, col_name in enumerate(columns, 1):
            cell = ws.cell(row=row_num, column=col_idx)
            value = cell.value if cell.value is not None else "(空)"
            print(f"  {col_name}: {value}")
        
        # 特别验证序号列和备注列
        index_cell = ws.cell(row=row_num, column=1)  # 序号列
        remark_cell = ws.cell(row=row_num, column=6)  # 备注列
        
        expected_index = expected_indexes[i]
        expected_remark = f"第{expected_index}项"
        
        if index_cell.value == expected_index:
            print(f"  ✅ 序号列正确: {index_cell.value}")
        else:
            print(f"  ❌ 序号列错误: 期望{expected_index}, 实际{index_cell.value}")
        
        if str(remark_cell.value) == expected_remark:
            print(f"  ✅ 备注列正确: {remark_cell.value}")
        else:
            print(f"  ❌ 备注列错误: 期望'{expected_remark}', 实际'{remark_cell.value}'")
    
    print("\n=== 完整表格内容 ===")
    print("行号 | 序号 | 编号 | 商品名称   | 规格     | 单位 | 备注")
    print("-" * 60)
    
    for row_num in data_rows:
        row_data = []
        for col_num in range(1, 7):  # 6列
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value is not None else "(空)"
            row_data.append(str(value))
        
        print(f" {row_num}   | {' | '.join(row_data)}")
    
    print("\n=== 验证结果 ===")
    
    # 验证序号列
    sequence_correct = True
    for i, row_num in enumerate(data_rows):
        index_cell = ws.cell(row=row_num, column=1)
        if index_cell.value != i + 1:
            sequence_correct = False
            break
    
    # 验证备注列
    remark_correct = True
    for i, row_num in enumerate(data_rows):
        remark_cell = ws.cell(row=row_num, column=6)
        expected_remark = f"第{i + 1}项"
        if str(remark_cell.value) != expected_remark:
            remark_correct = False
            break
    
    if sequence_correct and remark_correct:
        print("🎉 灵活 {{auto_index}} 功能完全正确！")
        print("   ✅ 序号列: 纯数字序号正确")
        print("   ✅ 备注列: 嵌入文本的序号正确")
        print("   ✅ 序号递增: 从1开始连续递增")
        print("   ✅ 文本替换: {{auto_index}} 正确替换为序号")
    else:
        print("❌ 灵活 {{auto_index}} 功能有问题")
        if not sequence_correct:
            print("   ❌ 序号列有问题")
        if not remark_correct:
            print("   ❌ 备注列有问题")
    
    print(f"\n📁 Excel文件位置: {latest_file}")
    
else:
    print("❌ 没有找到flexible_auto_index的Excel文件")
    
    # 检查是否有其他Excel文件
    all_excel_files = glob.glob("filled_outputs/filled_*.xlsx")
    if all_excel_files:
        print("\n找到的其他Excel文件:")
        for file in all_excel_files[-3:]:  # 显示最新的3个文件
            print(f"  {file}")

print("\n=== 验证完成 ===")
