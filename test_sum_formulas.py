import requests

# 测试SUM公式功能
data = {
    "data": {
        "list": [
            {"batno": "SUM001", "name": "求和测试产品1", "matrial": "材料1", "unit": "个"},
            {"batno": "SUM002", "name": "求和测试产品2", "matrial": "材料2", "unit": "套"},
            {"batno": "SUM003", "name": "求和测试产品3", "matrial": "材料3", "unit": "件"},
            {"batno": "SUM004", "name": "求和测试产品4", "matrial": "材料4", "unit": "台"}
        ],
        "name": "SUM公式测试客户",
        "creator": "公式测试员"
    },
    "template_name": "delivery_with_sum"  # 使用包含SUM公式的模板
}

print("🧮 测试SUM公式自动更新功能")
print("=" * 50)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 测试内容:")
    print("- 模板: delivery_with_sum.xlsx")
    print("- 包含SUM公式: =SUM(F5:F5), =SUM(H5:H5)")
    print("- 数据: 4行测试数据")
    print("- 预期: SUM公式范围自动更新为 F5:F8, H5:H8")
    
    print("\n🎯 SUM公式更新逻辑:")
    print("1. 检测模板中的SUM公式")
    print("2. 识别数据范围（第5行到第8行）")
    print("3. 自动更新公式范围")
    print("4. 保持其他公式不变")
    
    print("\n📊 预期效果:")
    print("- 原始公式: =SUM(F5:F5)")
    print("- 更新后: =SUM(F5:F8)")
    print("- 原始公式: =SUM(H5:H5)")
    print("- 更新后: =SUM(H5:H8)")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
    print("\n💡 SUM公式功能特点:")
    print("✅ 自动检测: 扫描所有单元格中的SUM公式")
    print("✅ 智能更新: 根据数据行数自动调整范围")
    print("✅ 列匹配: 只更新对应列的SUM公式")
    print("✅ 保持格式: 保持公式的其他部分不变")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 50)
print("🧮 SUM公式测试完成！")
print("请检查生成的Excel文件中的公式是否正确更新。")
