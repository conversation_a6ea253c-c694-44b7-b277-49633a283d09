import requests
import re

# 发送测试数据，专门检查红色字体
data = {
    "data": {
        "list": [
            {"batno": "TEST001", "name": "测试商品", "matrial": "材料", "unit": "个"}
        ],
        "name": "测试客户132",  # 这个应该是红色的
        "creator": "Super Admin"  # 这个也应该是红色的
    },
    "template_name": "delivery"
}

print("=== 检查HTML中的红色字体 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 获取显示页面的HTML
    display_response = requests.get("http://localhost:5000/display")
    html_content = display_response.text
    
    print("\n=== 搜索红色字体内容 ===")
    
    # 搜索包含"测试客户132"的HTML
    customer_pattern = r'<t[dh][^>]*>([^<]*测试客户132[^<]*)</t[dh]>'
    customer_matches = re.findall(customer_pattern, html_content)
    print(f"找到客户名称: {customer_matches}")
    
    # 搜索包含"Super Admin"的HTML
    admin_pattern = r'<t[dh][^>]*>([^<]*Super Admin[^<]*)</t[dh]>'
    admin_matches = re.findall(admin_pattern, html_content)
    print(f"找到创建者: {admin_matches}")
    
    # 搜索包含这些内容的完整单元格HTML（包括样式）
    print("\n=== 搜索完整的单元格HTML ===")
    
    # 搜索客户名称的完整单元格
    customer_cell_pattern = r'<t[dh][^>]*style="([^"]*)"[^>]*>([^<]*测试客户132[^<]*)</t[dh]>'
    customer_cell_matches = re.findall(customer_cell_pattern, html_content)
    
    if customer_cell_matches:
        for style, content in customer_cell_matches:
            print(f"客户名称单元格:")
            print(f"  内容: '{content}'")
            print(f"  样式: {style}")
            
            # 检查颜色
            color_match = re.search(r'color:\s*([^;]+)', style)
            if color_match:
                color = color_match.group(1).strip()
                print(f"  字体颜色: {color}")
                if color.upper() == '#FF0000':
                    print(f"  ✅ 正确的红色!")
                elif color.upper() == '#000000':
                    print(f"  ❌ 错误：显示为黑色")
                else:
                    print(f"  ⚠️ 其他颜色: {color}")
            else:
                print(f"  ❌ 没有找到颜色样式")
    else:
        print("❌ 没有找到客户名称的单元格")
    
    # 搜索创建者的完整单元格
    admin_cell_pattern = r'<t[dh][^>]*style="([^"]*)"[^>]*>([^<]*Super Admin[^<]*)</t[dh]>'
    admin_cell_matches = re.findall(admin_cell_pattern, html_content)
    
    if admin_cell_matches:
        for style, content in admin_cell_matches:
            print(f"\n创建者单元格:")
            print(f"  内容: '{content}'")
            print(f"  样式: {style}")
            
            # 检查颜色
            color_match = re.search(r'color:\s*([^;]+)', style)
            if color_match:
                color = color_match.group(1).strip()
                print(f"  字体颜色: {color}")
                if color.upper() == '#FF0000':
                    print(f"  ✅ 正确的红色!")
                elif color.upper() == '#000000':
                    print(f"  ❌ 错误：显示为黑色")
                else:
                    print(f"  ⚠️ 其他颜色: {color}")
            else:
                print(f"  ❌ 没有找到颜色样式")
    else:
        print("❌ 没有找到创建者的单元格")
    
    # 搜索所有包含红色的单元格
    print("\n=== 搜索所有红色单元格 ===")
    red_pattern = r'<t[dh][^>]*style="[^"]*color:\s*#FF0000[^"]*"[^>]*>([^<]*)</t[dh]>'
    red_matches = re.findall(red_pattern, html_content, re.IGNORECASE)
    
    if red_matches:
        print(f"找到 {len(red_matches)} 个红色单元格:")
        for i, content in enumerate(red_matches):
            print(f"  {i+1}. '{content}'")
    else:
        print("❌ 没有找到任何红色单元格!")
    
    # 搜索所有颜色样式
    print("\n=== 搜索所有颜色样式 ===")
    color_pattern = r'color:\s*([^;]+)'
    color_matches = re.findall(color_pattern, html_content)
    
    if color_matches:
        unique_colors = list(set(color_matches))
        print(f"找到的所有颜色: {unique_colors}")
        
        red_count = sum(1 for color in color_matches if color.strip().upper() == '#FF0000')
        black_count = sum(1 for color in color_matches if color.strip().upper() == '#000000')
        
        print(f"红色单元格数量: {red_count}")
        print(f"黑色单元格数量: {black_count}")
    else:
        print("❌ 没有找到任何颜色样式!")

else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 检查完成 ===")
