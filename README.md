# 📄 模板打印系统

一个支持Excel和Word模板的动态数据填充、样式保持和文件导出的Web应用系统。

## ✨ 主要功能

- 📊 **Excel模板处理**：支持动态列表、自增序号、SUM公式
- 📄 **Word模板处理**：支持复杂布局、字体样式、文本对齐
- 🎨 **样式保持**：完整保留原始模板的格式和样式
- 📥 **智能导出**：根据模板类型自动导出对应格式文件
- 🖨️ **打印优化**：纯白背景，适合正式文档打印
- 🌐 **Web界面**：用户友好的操作界面

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python app.py
```

### 3. 访问系统
打开浏览器访问：http://localhost:5000

## 📋 使用示例

### API调用示例
```bash
curl -X POST http://localhost:5000/api/print \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "list": [
        {"name": "产品A", "quantity": 10, "price": 100},
        {"name": "产品B", "quantity": 20, "price": 200}
      ],
      "customer": "测试客户",
      "date": "2025-06-21"
    },
    "template_name": "销售出库单"
  }'
```

### 支持的占位符
- `{{field_name}}` - 普通字段
- `{{list.field}}` - 动态列表字段
- `{{auto_index}}` - 自增序号
- `{{sum.field}}` - 字段求和

## 📊 模板类型

### Excel模板 (.xlsx)
- ✅ 动态列表扩展
- ✅ 自增序号生成
- ✅ SUM公式计算
- ✅ 样式完整保持
- ✅ 导出Excel文件

### Word模板 (.docx)
- ✅ 复杂文档布局
- ✅ 字体样式保持
- ✅ 文本对齐处理
- ✅ 表格结构保持
- ✅ 导出Word文件

## 🔌 API接口

| 接口 | 方法 | 功能 |
|------|------|------|
| `/api/print` | POST | 处理模板数据 |
| `/api/export/info` | GET | 获取导出文件信息 |
| `/api/export` | GET | 下载文件 |
| `/display` | GET | 查看处理结果 |
| `/print` | GET | 打印页面 |

## 📁 项目结构

```
print_sys/
├── app.py                    # 主应用程序
├── requirements.txt          # 依赖包
├── templates/               # 模板文件目录
│   ├── delivery.xlsx        # Excel模板示例
│   └── *.docx              # Word模板示例
├── filled_outputs/         # 输出文件目录
└── static/                 # 静态资源
```

## 🎯 核心特性

### 🔧 智能处理
- 自动识别模板类型
- 动态数据填充
- 样式完整保留

### 📥 导出功能
- Excel模板 → Excel文件
- Word模板 → Word文件
- 一键下载

### 🎨 用户体验
- 直观的Web界面
- 实时预览功能
- 友好的错误提示

## 📖 详细文档

- [项目结构说明](PROJECT_STRUCTURE.md)
- [自增序号功能](AUTO_INDEX_COMPLETE_GUIDE.md)
- [SUM公式功能](SUM_PLACEHOLDER_COMPLETE_GUIDE.md)
- [动态列表功能](dynamic_list_guide.md)

## 🛠️ 技术栈

- **后端**: Python + Flask
- **Excel处理**: openpyxl
- **Word处理**: python-docx
- **前端**: HTML + CSS + JavaScript

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**🎉 开始使用模板打印系统，让文档处理更简单！**
