from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_heading(doc, text, level=1):
    """添加标题"""
    heading = doc.add_heading(text, level)
    return heading

def add_paragraph(doc, text, style=None):
    """添加段落"""
    p = doc.add_paragraph(text, style)
    return p

def add_code_block(doc, code_text):
    """添加代码块"""
    p = doc.add_paragraph()
    run = p.add_run(code_text)
    run.font.name = 'Consolas'
    run.font.size = Pt(9)
    # 设置背景色
    p.style = 'No Spacing'
    return p

def create_manual():
    """创建使用说明书"""
    doc = Document()

    # 设置文档标题
    title = doc.add_heading('模板打印系统使用说明书', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 添加版本信息
    version_p = doc.add_paragraph('版本：2.0')
    version_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    date_p = doc.add_paragraph('更新日期：2023年12月')
    date_p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    doc.add_page_break()

    # 目录
    add_heading(doc, '目录', 1)
    toc_items = [
        '1. 系统概述',
        '2. 系统要求与安装',
        '3. 模板准备',
        '4. 列表功能详解',
        '5. API使用说明',
        '6. 查看和打印',
        '7. 与nocobase集成',
        '8. 常见问题解答',
        '9. 系统维护',
        '10. 联系与支持'
    ]

    for item in toc_items:
        doc.add_paragraph(item, style='List Number')

    doc.add_page_break()

    # 1. 系统概述
    add_heading(doc, '1. 系统概述', 1)
    add_paragraph(doc, '模板打印系统是一个基于Python开发的Web应用，用于接收打印请求，填充Excel或Word模板，并将结果渲染到HTML页面上供用户查看和打印。本系统主要面向需要从nocobase或其他系统接收数据并生成打印页面的场景。')

    add_heading(doc, '1.1 系统特点', 2)
    features = [
        '支持Excel和Word模板',
        '通过简单的API接口接收数据',
        '自动填充模板并生成打印页面',
        '纯白背景打印，无阴影框',
        '支持列表数据处理',
        '易于与其他系统集成',
        '提供完整的Web界面和API文档'
    ]

    for feature in features:
        doc.add_paragraph(feature, style='List Bullet')

    add_heading(doc, '1.2 应用场景', 2)
    scenarios = [
        '订单打印：从ERP系统接收订单数据，生成打印格式的订单单据',
        '报表生成：将数据库数据填充到预设模板，生成标准化报表',
        '证书制作：批量生成证书、合同等文档',
        '发票打印：自动填充发票模板并打印',
        '标签制作：生成商品标签、地址标签等'
    ]

    for scenario in scenarios:
        doc.add_paragraph(scenario, style='List Bullet')

    # 2. 系统要求与安装
    add_heading(doc, '2. 系统要求与安装', 1)

    add_heading(doc, '2.1 系统要求', 2)
    add_paragraph(doc, '本系统基于以下技术构建：')
    requirements = [
        'Python 3.6+',
        'Flask (Web框架)',
        'openpyxl (Excel处理)',
        'python-docx (Word处理)',
        'requests (HTTP请求，用于测试)'
    ]

    for req in requirements:
        doc.add_paragraph(req, style='List Bullet')

    add_heading(doc, '2.2 安装步骤', 2)
    install_steps = [
        '安装Python 3.6或更高版本',
        '下载项目文件到本地目录',
        '安装依赖包：pip install flask openpyxl python-docx',
        '启动应用：python app.py',
        '访问 http://localhost:5000 查看系统首页'
    ]

    for i, step in enumerate(install_steps, 1):
        doc.add_paragraph(f'{i}. {step}', style='List Number')

    # 3. 模板准备
    add_heading(doc, '3. 模板准备', 1)

    add_heading(doc, '3.1 Excel模板', 2)
    add_paragraph(doc, '创建Excel模板的步骤：')
    excel_steps = [
        '创建Excel文件（.xlsx格式）',
        '在需要填充数据的单元格中使用 {{变量名}} 格式的占位符',
        '将模板文件保存到系统的 templates 目录下'
    ]

    for i, step in enumerate(excel_steps, 1):
        doc.add_paragraph(f'{i}. {step}', style='List Number')

    add_paragraph(doc, '示例：')
    excel_examples = [
        '单元格A1: 打印日期: {{date}}',
        '单元格B2: 客户名称: {{customer}}',
        '单元格C3: 订单号: {{order_id}}'
    ]

    for example in excel_examples:
        doc.add_paragraph(example, style='List Bullet')

    add_heading(doc, '3.2 Word模板', 2)
    add_paragraph(doc, '创建Word模板的步骤：')
    word_steps = [
        '创建Word文件（.docx格式）',
        '在需要填充数据的位置使用 {{变量名}} 格式的占位符',
        '将模板文件保存到系统的 templates 目录下'
    ]

    for i, step in enumerate(word_steps, 1):
        doc.add_paragraph(f'{i}. {step}', style='List Number')

    add_paragraph(doc, '示例：')
    word_examples = [
        '申请日期: {{date}}',
        '申请人: {{applicant}}',
        '申请部门: {{department}}'
    ]

    for example in word_examples:
        doc.add_paragraph(example, style='List Bullet')

    # 4. 列表功能详解
    add_heading(doc, '4. 列表功能详解', 1)
    add_paragraph(doc, '系统支持多种方式处理列表数据，以满足不同的业务需求。')

    add_heading(doc, '4.1 方法1：固定数量的列表项（推荐）', 2)
    add_paragraph(doc, '这是最稳定和可控的方法，适合大多数场景。')

    add_paragraph(doc, 'Excel模板示例：')
    add_code_block(doc, '''A8: 序号    B8: 商品名称         C8: 数量              D8: 单价              E8: 小计
A9: 1       B9: {{items1_name}}  C9: {{items1_quantity}} D9: {{items1_price}}  E9: {{items1_total}}
A10: 2      B10: {{items2_name}} C10: {{items2_quantity}} D10: {{items2_price}} E10: {{items2_total}}
A11: 3      B11: {{items3_name}} C11: {{items3_quantity}} D11: {{items3_price}} E11: {{items3_total}}''')

    add_paragraph(doc, '对应的API请求：')
    add_code_block(doc, '''{
    "template_name": "商品清单",
    "data": {
        "items1_name": "苹果",
        "items1_quantity": "10",
        "items1_price": "5.00",
        "items1_total": "50.00",
        "items2_name": "香蕉",
        "items2_quantity": "20",
        "items2_price": "3.00",
        "items2_total": "60.00",
        "items3_name": "橙子",
        "items3_quantity": "15",
        "items3_price": "4.00",
        "items3_total": "60.00"
    }
}''')

    add_heading(doc, '4.2 方法2：使用列表数据结构（自动转换）', 2)
    add_paragraph(doc, '当您有结构化的列表数据时，系统会自动将其转换为索引格式。')

    add_paragraph(doc, 'API请求示例：')
    add_code_block(doc, '''{
    "template_name": "商品清单",
    "data": {
        "items": [
            {"name": "笔记本电脑", "quantity": "1", "price": "5000.00", "total": "5000.00"},
            {"name": "鼠标", "quantity": "2", "price": "50.00", "total": "100.00"},
            {"name": "键盘", "quantity": "1", "price": "200.00", "total": "200.00"}
        ]
    }
}''')

    add_paragraph(doc, '系统会自动转换为：')
    conversions = [
        'items1_name, items1_quantity, items1_price, items1_total',
        'items2_name, items2_quantity, items2_price, items2_total',
        'items3_name, items3_quantity, items3_price, items3_total'
    ]

    for conversion in conversions:
        doc.add_paragraph(conversion, style='List Bullet')

    add_heading(doc, '4.3 方法3：简单列表', 2)
    add_paragraph(doc, '适用于简单的列表数据。')

    add_paragraph(doc, 'API请求示例：')
    add_code_block(doc, '''{
    "data": {
        "products": ["产品A", "产品B", "产品C"]
    }
}''')

    add_paragraph(doc, '系统会自动转换为：')
    simple_conversions = [
        'products1: "产品A"',
        'products2: "产品B"',
        'products3: "产品C"'
    ]

    for conversion in simple_conversions:
        doc.add_paragraph(conversion, style='List Bullet')

    add_heading(doc, '4.4 Word模板中的列表', 2)
    add_paragraph(doc, '在Word模板中，您可以使用相同的占位符格式：')
    add_code_block(doc, '''商品清单：
1. 商品名称：{{items1_name}}，数量：{{items1_quantity}}，单价：{{items1_price}}
2. 商品名称：{{items2_name}}，数量：{{items2_quantity}}，单价：{{items2_price}}
3. 商品名称：{{items3_name}}，数量：{{items3_quantity}}，单价：{{items3_price}}''')

    # 5. API使用说明
    add_heading(doc, '5. API使用说明', 1)

    add_heading(doc, '5.1 打印请求API', 2)
    api_details = [
        '端点: /api/print',
        '方法: POST',
        '内容类型: application/json'
    ]

    for detail in api_details:
        doc.add_paragraph(detail, style='List Bullet')

    add_paragraph(doc, '请求格式：')
    add_code_block(doc, '''{
    "template_name": "模板名称",
    "data": {
        "key1": "value1",
        "key2": "value2",
        ...
    }
}''')

    add_paragraph(doc, '参数说明：')
    params = [
        'template_name: 模板文件名（不含扩展名，系统会自动查找.xlsx或.docx文件）',
        'data: 包含要填充到模板中的数据，键名应与模板中的占位符名称一致'
    ]

    for param in params:
        doc.add_paragraph(param, style='List Bullet')

    add_paragraph(doc, '响应格式：')
    add_code_block(doc, '''{
    "success": true,
    "message": "Print request processed successfully",
    "display_url": "/display"
}''')

    add_heading(doc, '5.2 示例请求', 2)
    add_paragraph(doc, '使用curl发送请求：')
    add_code_block(doc, '''curl -X POST -H "Content-Type: application/json" -d '{
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-01",
        "company": "Test Company",
        "contact": "John Doe",
        "phone": "13800138000",
        "item1_name": "Product 1",
        "item1_quantity": "10",
        "item1_price": "100",
        "item2_name": "Product 2",
        "item2_quantity": "5",
        "item2_price": "200",
        "total_amount": "2000",
        "remarks": "Test remarks"
    }
}' http://localhost:5000/api/print''')

    add_paragraph(doc, '使用Python发送请求：')
    add_code_block(doc, '''import requests

url = "http://localhost:5000/api/print"
data = {
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-01",
        "company": "Test Company",
        "contact": "John Doe",
        "phone": "13800138000",
        "item1_name": "Product 1",
        "item1_quantity": "10",
        "item1_price": "100",
        "item2_name": "Product 2",
        "item2_quantity": "5",
        "item2_price": "200",
        "total_amount": "2000",
        "remarks": "Test remarks"
    }
}

response = requests.post(url, json=data)
print(response.json())''')

    # 6. 查看和打印
    add_heading(doc, '6. 查看和打印', 1)

    add_heading(doc, '6.1 访问显示页面', 2)
    add_paragraph(doc, '发送打印请求后，系统会处理模板并生成HTML页面。您可以通过访问以下URL查看打印结果：')
    add_paragraph(doc, 'http://localhost:5000/display')

    add_heading(doc, '6.2 打印页面功能', 2)
    print_features = [
        '查看填充后的数据',
        '点击"打印"按钮直接打印页面',
        '点击"返回首页"返回系统首页'
    ]

    for feature in print_features:
        doc.add_paragraph(feature, style='List Bullet')

    add_heading(doc, '6.3 打印特性', 2)
    add_paragraph(doc, '本系统的打印页面具有以下特性：')
    print_specs = [
        '纯白背景：打印时使用纯白背景，无阴影框',
        '隐藏非打印元素：打印时自动隐藏页面头部、页脚和按钮等非打印元素',
        '适合打印的布局：打印时自动调整布局，使其更适合打印',
        '保持原始格式：Excel和Word模板的格式在打印时得到保持'
    ]

    for spec in print_specs:
        doc.add_paragraph(spec, style='List Bullet')

    # 7. 与nocobase集成
    add_heading(doc, '7. 与nocobase集成', 1)

    add_heading(doc, '7.1 配置nocobase', 2)
    add_paragraph(doc, '在nocobase中，您可以通过以下步骤配置与打印系统的集成：')
    nocobase_steps = [
        '创建一个动作按钮',
        '配置HTTP请求动作',
        '设置请求URL为打印系统的API端点',
        '配置请求方法为POST',
        '设置请求体格式为JSON',
        '配置请求数据映射，将nocobase中的字段映射到打印模板中的占位符'
    ]

    for i, step in enumerate(nocobase_steps, 1):
        doc.add_paragraph(f'{i}. {step}', style='List Number')

    add_heading(doc, '7.2 示例nocobase配置', 2)
    add_code_block(doc, '''{
  "type": "request",
  "method": "post",
  "url": "http://your-print-server:5000/api/print",
  "headers": {
    "Content-Type": "application/json"
  },
  "data": {
    "template_name": "dayinceshi",
    "data": {
      "date": "{{$date}}",
      "company": "{{$company}}",
      "contact": "{{$contact}}",
      "phone": "{{$phone}}",
      "item1_name": "{{$items[0].name}}",
      "item1_quantity": "{{$items[0].quantity}}",
      "item1_price": "{{$items[0].price}}",
      "item2_name": "{{$items[1].name}}",
      "item2_quantity": "{{$items[1].quantity}}",
      "item2_price": "{{$items[1].price}}",
      "total_amount": "{{$total_amount}}",
      "remarks": "{{$remarks}}"
    }
  },
  "successMessage": "打印请求已发送"
}''')

    # 8. 常见问题解答
    add_heading(doc, '8. 常见问题解答', 1)

    add_heading(doc, '8.1 模板未找到', 2)
    add_paragraph(doc, '问题: 系统返回"Template \'xxx\' not found"错误')
    add_paragraph(doc, '解决方案:')
    template_solutions = [
        '确认模板文件已放置在templates目录下',
        '确认模板名称拼写正确（不含扩展名）',
        '确认模板文件格式为.xlsx或.docx'
    ]

    for solution in template_solutions:
        doc.add_paragraph(solution, style='List Bullet')

    add_heading(doc, '8.2 数据未正确填充', 2)
    add_paragraph(doc, '问题: 模板中的占位符未被替换')
    add_paragraph(doc, '解决方案:')
    data_solutions = [
        '确认占位符格式为{{变量名}}',
        '确认请求中的数据键名与模板中的占位符名称一致',
        '检查日志文件查看详细错误信息',
        '确保数据类型正确（字符串、数字等）'
    ]

    for solution in data_solutions:
        doc.add_paragraph(solution, style='List Bullet')

    add_heading(doc, '8.3 列表数据处理问题', 2)
    add_paragraph(doc, '问题: 列表数据未正确显示')
    add_paragraph(doc, '解决方案:')
    list_solutions = [
        '使用固定数量的列表项方法（推荐）',
        '确保列表数据结构正确',
        '检查占位符命名规则（如items1_name, items2_name）',
        '验证JSON数据格式是否正确'
    ]

    for solution in list_solutions:
        doc.add_paragraph(solution, style='List Bullet')

    add_heading(doc, '8.4 打印时仍有阴影或背景色', 2)
    add_paragraph(doc, '问题: 打印预览或实际打印时仍显示阴影或背景色')
    add_paragraph(doc, '解决方案:')
    print_solutions = [
        '在浏览器的打印设置中勾选"背景图形"选项',
        '尝试使用不同的浏览器进行打印',
        '检查CSS样式是否正确应用',
        '确保使用最新版本的浏览器'
    ]

    for solution in print_solutions:
        doc.add_paragraph(solution, style='List Bullet')

    add_heading(doc, '8.5 API请求失败', 2)
    add_paragraph(doc, '问题: 无法成功发送API请求')
    add_paragraph(doc, '解决方案:')
    api_solutions = [
        '确认服务器正在运行（python app.py）',
        '检查端口是否被占用',
        '验证请求URL是否正确',
        '确认Content-Type设置为application/json',
        '检查防火墙设置'
    ]

    for solution in api_solutions:
        doc.add_paragraph(solution, style='List Bullet')

    # 9. 系统维护
    add_heading(doc, '9. 系统维护', 1)

    add_heading(doc, '9.1 日志查看', 2)
    add_paragraph(doc, '系统日志保存在app.log文件中，您可以查看此文件以获取详细的运行信息和错误报告。')
    add_paragraph(doc, '日志级别包括：')
    log_levels = [
        'INFO: 一般信息，如请求处理',
        'ERROR: 错误信息，如模板处理失败',
        'DEBUG: 调试信息（调试模式下）'
    ]

    for level in log_levels:
        doc.add_paragraph(level, style='List Bullet')

    add_heading(doc, '9.2 清理输出文件', 2)
    add_paragraph(doc, '系统会在filled_outputs目录中保存所有填充后的文件。为避免磁盘空间占用过大，建议定期清理此目录中的旧文件。')
    add_paragraph(doc, '清理建议：')
    cleanup_tips = [
        '每周清理一次超过7天的文件',
        '保留重要的输出文件作为备份',
        '可以编写脚本自动清理旧文件'
    ]

    for tip in cleanup_tips:
        doc.add_paragraph(tip, style='List Bullet')

    add_heading(doc, '9.3 性能优化', 2)
    add_paragraph(doc, '系统性能优化建议：')
    performance_tips = [
        '定期重启应用程序以释放内存',
        '监控系统资源使用情况',
        '优化模板文件大小',
        '使用生产环境WSGI服务器（如Gunicorn）',
        '配置反向代理（如Nginx）'
    ]

    for tip in performance_tips:
        doc.add_paragraph(tip, style='List Bullet')

    # 10. 联系与支持
    add_heading(doc, '10. 联系与支持', 1)
    add_paragraph(doc, '如有任何问题或需要技术支持，请联系系统管理员或开发人员。')

    add_heading(doc, '10.1 技术支持', 2)
    support_info = [
        '系统版本：2.0',
        '更新日期：2023年12月',
        '支持的Python版本：3.6+',
        '支持的操作系统：Windows, Linux, macOS'
    ]

    for info in support_info:
        doc.add_paragraph(info, style='List Bullet')

    add_heading(doc, '10.2 更新历史', 2)
    add_paragraph(doc, '版本更新历史：')
    version_history = [
        'v2.0 (2023-12): 添加列表功能支持，优化打印样式',
        'v1.0 (2023-11): 初始版本，支持基本的Excel和Word模板处理'
    ]

    for version in version_history:
        doc.add_paragraph(version, style='List Bullet')

    # 添加页脚
    doc.add_page_break()
    footer = doc.add_paragraph('© 2023 模板打印系统 版权所有')
    footer.alignment = WD_ALIGN_PARAGRAPH.CENTER

    return doc

# 生成文档
if __name__ == "__main__":
    document = create_manual()
    document.save('模板打印系统使用说明书.docx')
    print("说明书已生成：模板打印系统使用说明书.docx")
