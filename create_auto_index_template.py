import openpyxl
from copy import copy

# 创建带有 {{auto_index}} 占位符的新模板
print("=== 创建带有 {{auto_index}} 占位符的模板 ===")

# 加载原始模板
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("原始模板第5行内容:")
for col in range(1, 6):
    cell = ws.cell(row=5, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 在第1列（序号列）添加 {{auto_index}} 占位符
sequence_cell = ws.cell(row=5, column=1)
sequence_cell.value = "{{auto_index}}"

print("\n修改后的模板第5行内容:")
for col in range(1, 6):
    cell = ws.cell(row=5, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 保存新模板
new_template_path = 'templates/delivery_with_auto_index.xlsx'
wb.save(new_template_path)
print(f"\n✅ 新模板已保存: {new_template_path}")

print("\n=== 新模板结构 ===")
print("第1列: {{auto_index}} - 自增序号")
print("第2列: {{list.batno}} - 批号")
print("第3列: {{list.name}} - 名称")
print("第4列: {{list.matrial}} - 材质")
print("第5列: {{list.unit}} - 单位")

print("\n=== 使用说明 ===")
print("1. 使用模板名称: 'delivery_with_auto_index'")
print("2. {{auto_index}} 会自动生成序号: 1, 2, 3, 4...")
print("3. 可以将 {{auto_index}} 放在任意列")
print("4. 支持多个 {{auto_index}} 占位符（都会显示相同序号）")

# 验证模板
print("\n=== 验证新模板 ===")
wb_verify = openpyxl.load_workbook(new_template_path)
ws_verify = wb_verify.active

print("验证第5行内容:")
for col in range(1, 6):
    cell = ws_verify.cell(row=5, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 检查是否包含 {{auto_index}}
auto_index_found = False
for col in range(1, ws_verify.max_column + 1):
    cell = ws_verify.cell(row=5, column=col)
    if cell.value and "{{auto_index}}" in str(cell.value):
        auto_index_found = True
        print(f"✅ 在第{col}列找到 {{{{auto_index}}}} 占位符")

if auto_index_found:
    print("✅ 新模板创建成功，包含 {{auto_index}} 占位符")
else:
    print("❌ 新模板创建失败，未找到 {{auto_index}} 占位符")

print("\n=== 创建完成 ===")
