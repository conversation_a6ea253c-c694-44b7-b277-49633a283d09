<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红色字体测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-table {
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .test-table td {
            border: 1px solid #000;
            padding: 10px;
            background-color: white;
        }
        
        /* 测试不同的红色样式 */
        .red-inline {
            color: #FF0000;
        }
        
        .red-important {
            color: #FF0000 !important;
        }
    </style>
</head>
<body>
    <h1>红色字体测试页面</h1>
    
    <h2>1. 基础红色测试</h2>
    <p style="color: #FF0000;">这是内联样式的红色文字</p>
    <p class="red-inline">这是CSS类的红色文字</p>
    <p class="red-important">这是!important的红色文字</p>
    
    <h2>2. 表格中的红色测试</h2>
    <table class="test-table">
        <tr>
            <td style="color: #FF0000;">内联红色</td>
            <td class="red-inline">CSS红色</td>
            <td class="red-important">!important红色</td>
        </tr>
        <tr>
            <td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; color: #FF0000; text-align: center; vertical-align: middle; background-color: #FFFFFF">
                完全相同的样式：测试客户132
            </td>
            <td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; color: #FF0000; text-align: center; vertical-align: middle; background-color: #FFFFFF">
                完全相同的样式：Super Admin
            </td>
            <td style="color: #000000;">黑色对比</td>
        </tr>
    </table>
    
    <h2>3. 不同红色值测试</h2>
    <table class="test-table">
        <tr>
            <td style="color: #FF0000;">#FF0000</td>
            <td style="color: red;">red</td>
            <td style="color: rgb(255,0,0);">rgb(255,0,0)</td>
            <td style="color: #ff0000;">#ff0000 (小写)</td>
        </tr>
    </table>
    
    <h2>4. 浏览器信息</h2>
    <p>用户代理: <span id="userAgent"></span></p>
    <p>颜色支持测试:</p>
    <div style="color: #FF0000; font-weight: bold; font-size: 18px;">
        如果这行文字是红色的，说明浏览器支持红色显示
    </div>
    
    <script>
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        // 检查计算样式
        window.onload = function() {
            const redElements = document.querySelectorAll('[style*="color: #FF0000"]');
            console.log('找到红色元素数量:', redElements.length);
            
            redElements.forEach((element, index) => {
                const computedStyle = window.getComputedStyle(element);
                const color = computedStyle.color;
                console.log(`元素 ${index + 1} 计算样式颜色:`, color);
                console.log(`元素 ${index + 1} 内容:`, element.textContent.trim());
            });
        };
    </script>
</body>
</html>
