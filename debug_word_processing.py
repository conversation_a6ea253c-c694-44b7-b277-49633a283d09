from docx import Document
import os

# 测试Word文档处理
print("=== 测试Word文档处理 ===")

try:
    # 检查Word模板是否存在
    template_path = 'templates/委外加工过程不良品记录表.docx'
    if os.path.exists(template_path):
        print(f"✅ Word模板存在: {template_path}")
        
        # 尝试加载Word文档
        doc = Document(template_path)
        print(f"✅ Word文档加载成功")
        
        # 检查文档内容
        print(f"段落数量: {len(doc.paragraphs)}")
        print(f"表格数量: {len(doc.tables)}")
        
        # 显示前几个段落
        print("\n前5个段落内容:")
        for i, para in enumerate(doc.paragraphs[:5]):
            if para.text.strip():
                print(f"  段落{i+1}: {para.text[:50]}...")
        
        # 显示表格信息
        print(f"\n表格信息:")
        for i, table in enumerate(doc.tables):
            print(f"  表格{i+1}: {len(table.rows)}行 x {len(table.columns)}列")
            
            # 显示第一行内容
            if table.rows:
                first_row = table.rows[0]
                row_text = []
                for cell in first_row.cells:
                    cell_text = cell.text.strip()[:20] if cell.text.strip() else "(空)"
                    row_text.append(cell_text)
                print(f"    第1行: {' | '.join(row_text)}")
        
        # 查找占位符
        print(f"\n查找占位符:")
        placeholder_count = 0
        
        # 在段落中查找
        for para in doc.paragraphs:
            if para.text and '{{' in para.text:
                print(f"  段落占位符: {para.text}")
                placeholder_count += 1
        
        # 在表格中查找
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    if cell.text and '{{' in cell.text:
                        print(f"  表格{table_idx+1}第{row_idx+1}行第{cell_idx+1}列: {cell.text}")
                        placeholder_count += 1
        
        print(f"总共找到 {placeholder_count} 个占位符")
        
        # 测试简单的占位符替换
        print(f"\n测试占位符替换:")
        test_data = {
            'ccdkh': 'TEST123',
            'name': '测试客户',
            'creator': '测试员'
        }
        
        replaced_count = 0
        
        # 替换段落中的占位符
        for para in doc.paragraphs:
            if para.text:
                original_text = para.text
                new_text = original_text
                
                for key, value in test_data.items():
                    placeholder = f"{{{{{key}}}}}"
                    if placeholder in new_text:
                        new_text = new_text.replace(placeholder, str(value))
                        replaced_count += 1
                
                if new_text != original_text:
                    para.text = new_text
                    print(f"  段落替换: '{original_text}' -> '{new_text}'")
        
        # 替换表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text:
                        original_text = cell.text
                        new_text = original_text
                        
                        for key, value in test_data.items():
                            placeholder = f"{{{{{key}}}}}"
                            if placeholder in new_text:
                                new_text = new_text.replace(placeholder, str(value))
                                replaced_count += 1
                        
                        if new_text != original_text:
                            cell.text = new_text
                            print(f"  表格替换: '{original_text}' -> '{new_text}'")
        
        print(f"总共替换了 {replaced_count} 个占位符")
        
        # 尝试保存文档
        output_path = 'debug_word_output.docx'
        doc.save(output_path)
        print(f"✅ Word文档保存成功: {output_path}")
        
        print(f"\n✅ Word处理测试完全成功！")
        
    else:
        print(f"❌ Word模板不存在: {template_path}")

except Exception as e:
    print(f"❌ Word处理测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Word处理测试完成 ===")
