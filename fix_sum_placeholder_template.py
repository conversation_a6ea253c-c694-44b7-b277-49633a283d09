import openpyxl

# 修复SUM占位符模板的字段名问题
print("=== 修复SUM占位符模板 ===")

# 加载模板
wb = openpyxl.load_workbook('templates/delivery_with_sum_placeholders.xlsx')
ws = wb.active

print("修复前的合计行:")
for col in range(1, 10):
    cell = ws.cell(row=6, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 修复SUM占位符的字段名
# 第6列：数量合计，应该对应 {{list.quantity}}
qty_sum_cell = ws.cell(row=6, column=6)
qty_sum_cell.value = "{{sum.quantity}}"  # 修正为 quantity

# 第8列：金额合计，保持 {{sum.amount}}
amount_sum_cell = ws.cell(row=6, column=8)
amount_sum_cell.value = "{{sum.amount}}"  # 保持不变

print("\n修复后的合计行:")
for col in range(1, 10):
    cell = ws.cell(row=6, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

# 保存修复后的模板
wb.save('templates/delivery_with_sum_placeholders.xlsx')
print(f"\n✅ 模板已修复并保存")

print("\n=== 字段名映射 ===")
print("模板字段 -> 数据字段:")
print("{{list.batno}} -> batno")
print("{{list.name}} -> name")
print("{{list.matrial}} -> matrial")
print("{{list.unit}} -> unit")
print("{{list.quantity}} -> quantity")
print("{{list.price}} -> price")
print("{{list.amount}} -> amount")
print("{{list.note}} -> note")

print("\nSUM占位符:")
print("{{sum.quantity}} -> 计算所有 quantity 字段的总和")
print("{{sum.amount}} -> 计算所有 amount 字段的总和")

print("\n=== 正确的JSON数据格式 ===")
print("""
{
  "template_name": "delivery_with_sum_placeholders",
  "data": {
    "list": [
      {"batno": "A001", "name": "产品A", "matrial": "材料A", "unit": "个", "quantity": 10, "price": 100, "amount": 1000, "note": "备注A"},
      {"batno": "B002", "name": "产品B", "matrial": "材料B", "unit": "套", "quantity": 20, "price": 200, "amount": 4000, "note": "备注B"}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
""")

print("注意：字段名必须与模板中的占位符完全匹配！")
print("- quantity (不是 qty)")
print("- note (不是 remark)")

print("\n=== 修复完成 ===")
