import requests

# 测试包含红色字体的数据
data = {
    "data": {
        "list": [
            {"batno": "RED001", "name": "红色测试1", "matrial": "材料A", "unit": "个"},
            {"batno": "RED002", "name": "红色测试2", "matrial": "材料B", "unit": "套"},
            {"batno": "RED003", "name": "红色测试3", "matrial": "材料C", "unit": "件"}
        ],
        "name": "颜色测试客户",  # 这个字段在模板中是红色的
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 测试字体颜色显示 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    print("请检查浏览器中的显示效果：")
    print("1. 大部分文字应该是黑色")
    print("2. '颜色测试客户' 应该是红色")
    print("3. 所有数据行应该清晰可见，不是灰色")
    print()
    print("🔗 查看链接:")
    print("   普通页面: http://localhost:5000/display")
    print("   打印页面: http://localhost:5000/print")
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 测试完成 ===")
