import openpyxl
import re

# 加载模板文件
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 调试模板处理过程 ===")
print(f"工作表最大行数: {ws.max_row}")
print(f"工作表最大列数: {ws.max_column}")

# 查找所有包含占位符的单元格
print("\n1. 查找所有占位符:")
placeholder_cells = []
for row_idx, row in enumerate(ws.iter_rows(), 1):
    for col_idx, cell in enumerate(row, 1):
        if cell.value and isinstance(cell.value, str):
            # 查找各种占位符
            if '{{' in cell.value and '}}' in cell.value:
                placeholder_cells.append({
                    'row': row_idx,
                    'col': col_idx,
                    'value': cell.value
                })
                print(f"  第{row_idx}行第{col_idx}列: {cell.value}")

# 查找{{list:}}标记
print("\n2. 查找{{list:}}标记:")
list_markers = []
for cell in placeholder_cells:
    if "{{list:" in cell['value']:
        match = re.search(r'\{\{list:(\w+)\}\}', cell['value'])
        if match:
            list_markers.append(cell)
            print(f"  找到标记: {cell['value']} 在第{cell['row']}行第{cell['col']}列")

# 查找{{list.field}}占位符
print("\n3. 查找{{list.field}}占位符:")
list_field_cells = []
for cell in placeholder_cells:
    if re.search(r'\{\{list\.\w+\}\}', cell['value']):
        list_field_cells.append(cell)
        print(f"  找到占位符: {cell['value']} 在第{cell['row']}行第{cell['col']}列")

# 模拟自动检测逻辑
print("\n4. 模拟自动检测逻辑:")
if not list_markers and list_field_cells:
    first_row = min(cell['row'] for cell in list_field_cells)
    print(f"  没有找到{{list:}}标记，但找到{{list.field}}占位符")
    print(f"  第一个{{list.field}}在第{first_row}行")
    print(f"  将在第{first_row-1}行创建虚拟标记")
    
    # 分析模板行结构
    print(f"\n5. 分析第{first_row}行的模板结构:")
    template_row = first_row
    column_templates = {}
    
    for col_idx in range(1, ws.max_column + 1):
        template_cell = ws.cell(row=template_row, column=col_idx)
        if template_cell.value and isinstance(template_cell.value, str):
            # 查找{{list.field}}占位符
            list_matches = re.findall(r'\{\{list\.(\w+)\}\}', template_cell.value)
            if list_matches:
                column_templates[col_idx] = {
                    'field': list_matches[0],
                    'template': template_cell.value
                }
                print(f"    第{col_idx}列: {template_cell.value} -> 字段: {list_matches[0]}")
    
    print(f"\n6. 找到的列模板: {len(column_templates)}个")
    for col_idx, template_info in column_templates.items():
        print(f"    列{col_idx}: {template_info['field']}")

# 测试数据
test_data = {
    "list": [
        {
            "batno": "TEST001",
            "name": "测试商品1",
            "matrial": "塑料",
            "unit": "个",
            "quantity": 5
        },
        {
            "batno": "TEST002",
            "name": "测试商品2", 
            "matrial": None,
            "unit": "套",
            "quantity": 3
        }
    ]
}

print(f"\n7. 测试数据:")
print(f"  列表项数量: {len(test_data['list'])}")
for i, item in enumerate(test_data['list']):
    print(f"  项目{i+1}: {item}")

print("\n8. 预期结果:")
if list_field_cells:
    template_row = min(cell['row'] for cell in list_field_cells)
    print(f"  将在第{template_row}行开始插入数据")
    print(f"  需要插入{len(test_data['list'])}行数据")
    print(f"  第一行替换模板行，额外插入{len(test_data['list'])-1}行")
else:
    print("  未找到可处理的占位符")

print("\n=== 调试完成 ===")
