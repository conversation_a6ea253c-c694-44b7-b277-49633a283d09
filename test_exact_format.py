import requests
import json

# 使用您提供的确切数据格式
data = {
    "data": {
        "list": [
            {
                "createdAt": "2025-05-28T07:32:46.190Z",
                "updatedAt": "2025-05-28T09:44:59.152Z",
                "id": 1,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a",
                "name": "sdad",
                "matrial": "wd",
                "unit": "个",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.156Z",
                "updatedAt": "2025-05-28T09:44:59.156Z",
                "id": 2,
                "createdById": 1,
                "updatedById": 1,
                "batno": "ds",
                "name": "da",
                "matrial": None,
                "unit": "发a",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            }
        ],
        "name": "测试客户132",
        "creator": "Super Admin"
    },
    "template_name": "delivery"
}

print("=== 测试确切数据格式 ===")
print("发送数据到 API...")

response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ 请检查结果:")
    print("1. 访问: http://localhost:5000/display")
    print("2. 查看第5行是否显示: a | sdad | wd | 个")
    print("3. 查看第6行是否显示: ds | da | (空) | 发a")
else:
    print(f"❌ 失败: {response.text}")
