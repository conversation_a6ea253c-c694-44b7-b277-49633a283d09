import openpyxl
import os

# 直接测试Excel写入功能
print("=== 直接测试Excel写入功能 ===")

# 加载模板
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("原始第5行和第6行:")
for row in [5, 6]:
    row_data = []
    for col in range(1, 6):
        cell = ws.cell(row=row, column=col)
        value = cell.value if cell.value else ''
        row_data.append(f"'{value}'")
    print(f"第{row}行: {row_data}")

print("\n=== 在第6行插入新行 ===")
ws.insert_rows(6)

print("插入后的第5行、第6行和第7行:")
for row in [5, 6, 7]:
    row_data = []
    for col in range(1, 6):
        cell = ws.cell(row=row, column=col)
        value = cell.value if cell.value else ''
        row_data.append(f"'{value}'")
    print(f"第{row}行: {row_data}")

print("\n=== 设置第6行数据 ===")
# 设置第6行的数据
ws.cell(row=6, column=2, value="TEST_BATNO")
ws.cell(row=6, column=3, value="TEST_NAME")
ws.cell(row=6, column=4, value="TEST_MATERIAL")
ws.cell(row=6, column=5, value="TEST_UNIT")

print("设置数据后的第6行:")
row_data = []
for col in range(1, 6):
    cell = ws.cell(row=6, column=col)
    value = cell.value if cell.value else ''
    row_data.append(f"'{value}'")
print(f"第6行: {row_data}")

print("\n=== 保存文件 ===")
output_file = 'test_excel_write_output.xlsx'
wb.save(output_file)
print(f"文件已保存为: {output_file}")

print("\n=== 重新读取文件验证 ===")
wb2 = openpyxl.load_workbook(output_file)
ws2 = wb2.active

print("重新读取的第6行:")
row_data = []
for col in range(1, 6):
    cell = ws2.cell(row=6, column=col)
    value = cell.value if cell.value else ''
    row_data.append(f"'{value}'")
print(f"第6行: {row_data}")

# 检查是否有合并单元格影响
print("\n=== 检查合并单元格 ===")
merged_ranges = list(ws2.merged_cells.ranges)
print(f"合并单元格数量: {len(merged_ranges)}")

for i, merged_range in enumerate(merged_ranges):
    if 6 in range(merged_range.min_row, merged_range.max_row + 1):
        print(f"⚠️ 第6行被合并单元格影响: {merged_range}")

print("\n=== 测试完成 ===")
if os.path.exists(output_file):
    print(f"测试文件: {output_file}")
else:
    print("❌ 测试文件未生成")
