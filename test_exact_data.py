import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 使用您提供的确切数据格式
print("=== 测试您提供的确切数据格式 ===")

# 您的原始数据
data = {
    "data": {
        "list": [
            {
                "createdAt": "2025-05-28T07:32:46.190Z",
                "updatedAt": "2025-05-28T09:44:59.152Z",
                "id": 1,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a",
                "name": "sdad",
                "matrial": "wd",
                "unit": "个",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.156Z",
                "updatedAt": "2025-05-28T09:44:59.156Z",
                "id": 2,
                "createdById": 1,
                "updatedById": 1,
                "batno": "ds",
                "name": "da",
                "matrial": None,
                "unit": "发a",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.158Z",
                "updatedAt": "2025-05-28T09:44:59.158Z",
                "id": 3,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a das",
                "name": "阿萨 ",
                "matrial": "阿发",
                "unit": "阿发",
                "quantity": 3,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.160Z",
                "updatedAt": "2025-05-28T09:44:59.160Z",
                "id": 4,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a阿发",
                "name": "eqa'd",
                "matrial": None,
                "unit": "阿发",
                "quantity": 4,
                "note": None,
                "amount": None,
                "delivery_f": 1
            }
        ],
        "name": "测试客户132",
        "creator": "Super Admin"
    },
    "template_name": "delivery"
}

print("发送的数据结构:")
print(json.dumps(data, indent=2, ensure_ascii=False))

response = requests.post(url, json=data)
print(f"\n状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ API调用成功！")
    print("请访问以下链接查看结果:")
    print("1. 普通显示: http://localhost:5000/display")
    print("2. 专用打印: http://localhost:5000/print")
    
    print("\n🔍 预期结果:")
    print("第1行: a | sdad | wd | 个 | 2 | (空) | 1")
    print("第2行: ds | da | (空) | 发a | 2 | (空) | 1")
    print("第3行: a das | 阿萨 | 阿发 | 阿发 | 3 | (空) | 1")
    print("第4行: a阿发 | eqa'd | (空) | 阿发 | 4 | (空) | 1")
    
else:
    print(f"\n❌ API调用失败: {response.text}")

print("\n" + "="*60)
print("数据结构分析:")
print("✅ template_name: 'delivery'")
print("✅ data.list: 包含4个项目")
print("✅ data.name: '测试客户132'")
print("✅ data.creator: 'Super Admin'")
print("✅ 每个列表项包含所需字段: batno, name, matrial, unit, quantity, note, delivery_f")
