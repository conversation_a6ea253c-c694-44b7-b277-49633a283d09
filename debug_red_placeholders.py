import openpyxl

# 检查模板中的红色占位符
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查模板中的红色字体 ===")

# 遍历所有单元格，查找红色字体
red_cells = []

for row_idx in range(1, ws.max_row + 1):
    for col_idx in range(1, ws.max_column + 1):
        cell = ws.cell(row=row_idx, column=col_idx)
        
        if cell.font and cell.font.color:
            color = cell.font.color
            
            # 检查是否是红色
            is_red = False
            color_desc = ""
            
            # 检查RGB红色
            if color.rgb and isinstance(color.rgb, str):
                rgb = color.rgb
                if len(rgb) == 8:
                    rgb = rgb[2:]  # 移除alpha
                if rgb.upper() in ['FF0000', 'FF0000']:  # 纯红色
                    is_red = True
                    color_desc = f"RGB: #{rgb}"
            
            # 检查主题颜色（红色通常是theme=2或其他）
            elif hasattr(color, 'theme') and color.theme is not None:
                # 一些常见的红色主题
                if color.theme in [2, 6]:  # 可能的红色主题
                    is_red = True
                    color_desc = f"主题: {color.theme}"
            
            # 检查索引颜色
            elif hasattr(color, 'indexed') and color.indexed is not None:
                # 索引10通常是红色
                if color.indexed == 10:
                    is_red = True
                    color_desc = f"索引: {color.indexed}"
            
            # 如果不确定，显示所有颜色信息
            if not is_red and cell.value and '{{' in str(cell.value):
                print(f"占位符单元格 ({row_idx},{col_idx}): '{cell.value}'")
                print(f"  颜色对象: {color}")
                if hasattr(color, 'rgb'):
                    print(f"  RGB: {color.rgb}")
                if hasattr(color, 'theme'):
                    print(f"  主题: {color.theme}")
                if hasattr(color, 'indexed'):
                    print(f"  索引: {color.indexed}")
                if hasattr(color, 'tint'):
                    print(f"  色调: {color.tint}")
                print()
            
            if is_red:
                red_cells.append({
                    'row': row_idx,
                    'col': col_idx,
                    'value': cell.value,
                    'color_desc': color_desc
                })

print(f"找到 {len(red_cells)} 个红色字体单元格:")
for cell_info in red_cells:
    print(f"  ({cell_info['row']},{cell_info['col']}): '{cell_info['value']}' - {cell_info['color_desc']}")

print("\n=== 检查所有包含占位符的单元格 ===")
placeholder_cells = []

for row_idx in range(1, ws.max_row + 1):
    for col_idx in range(1, ws.max_column + 1):
        cell = ws.cell(row=row_idx, column=col_idx)
        
        if cell.value and '{{' in str(cell.value):
            color_info = "无颜色"
            if cell.font and cell.font.color:
                color = cell.font.color
                if hasattr(color, 'rgb') and color.rgb:
                    color_info = f"RGB: {color.rgb}"
                elif hasattr(color, 'theme') and color.theme is not None:
                    color_info = f"主题: {color.theme}"
                elif hasattr(color, 'indexed') and color.indexed is not None:
                    color_info = f"索引: {color.indexed}"
            
            placeholder_cells.append({
                'row': row_idx,
                'col': col_idx,
                'value': cell.value,
                'color_info': color_info
            })

print(f"找到 {len(placeholder_cells)} 个占位符单元格:")
for cell_info in placeholder_cells:
    print(f"  ({cell_info['row']},{cell_info['col']}): '{cell_info['value']}' - {cell_info['color_info']}")

print("\n=== 测试样式提取函数 ===")

def extract_cell_style_debug(cell):
    """调试版本的样式提取"""
    style = {}
    
    if cell.font:
        if cell.font.name:
            style['font-family'] = cell.font.name
        if cell.font.size:
            style['font-size'] = f"{cell.font.size}pt"
        if cell.font.bold:
            style['font-weight'] = 'bold'
        if cell.font.italic:
            style['font-style'] = 'italic'
        if cell.font.color:
            try:
                color = cell.font.color
                
                # 处理RGB颜色
                if color.rgb and isinstance(color.rgb, str):
                    color_rgb = color.rgb
                    if len(color_rgb) == 8:
                        color_rgb = color_rgb[2:]
                    elif len(color_rgb) == 6:
                        pass
                    else:
                        color_rgb = None

                    if color_rgb and len(color_rgb) == 6:
                        style['color'] = f"#{color_rgb}"
                
                # 处理主题颜色
                elif hasattr(color, 'theme') and color.theme is not None:
                    theme_colors = {
                        0: '#FFFFFF',  # 白色
                        1: '#000000',  # 黑色
                        2: '#1F497D',  # 深蓝色
                        3: '#4F81BD',  # 蓝色
                        4: '#9CBB58',  # 绿色
                        5: '#8064A2',  # 紫色
                        6: '#F79646',  # 橙色
                        7: '#4BACC6',  # 青色
                        8: '#F2F2F2',  # 浅灰色
                        9: '#808080',  # 灰色
                    }
                    
                    if color.theme in theme_colors:
                        style['color'] = theme_colors[color.theme]
                    else:
                        style['color'] = '#000000'
                
                else:
                    style['color'] = '#000000'
                    
            except Exception:
                style['color'] = '#000000'
        else:
            style['color'] = '#000000'
    
    style['background-color'] = '#FFFFFF'
    return style

# 测试占位符单元格的样式提取
print("\n占位符单元格的样式提取结果:")
for cell_info in placeholder_cells:
    cell = ws.cell(row=cell_info['row'], column=cell_info['col'])
    style = extract_cell_style_debug(cell)
    print(f"  {cell_info['value']}: {style}")
    
    if 'color' in style:
        color = style['color']
        if color == '#FF0000':
            print(f"    ✅ 红色字体")
        elif color == '#000000':
            print(f"    ⚠️ 黑色字体")
        else:
            print(f"    🎨 其他颜色: {color}")

print("\n=== 检查完成 ===")
