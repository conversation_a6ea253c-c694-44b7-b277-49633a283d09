import requests

# 测试合并单元格修复
data = {
    "data": {
        "list": [
            {"batno": "MERGE001", "name": "合并测试产品1", "matrial": "材料1", "unit": "个"},
            {"batno": "MERGE002", "name": "合并测试产品2", "matrial": "材料2", "unit": "套"},
            {"batno": "MERGE003", "name": "合并测试产品3", "matrial": "材料3", "unit": "件"},
            {"batno": "MERGE004", "name": "合并测试产品4", "matrial": "材料4", "unit": "台"}
        ],
        "name": "合并单元格测试客户",
        "creator": "合并测试员"
    },
    "template_name": "delivery"
}

print("🔧 测试合并单元格修复功能")
print("=" * 50)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 测试内容:")
    print("- 模板: delivery.xlsx")
    print("- 数据: 4行测试数据")
    print("- 重点: 检查合计行是否正确合并")
    
    print("\n🎯 预期效果:")
    print("1. 序号列：1, 2, 3, 4")
    print("2. 数据正确填充到各列")
    print("3. 合计行应该正确合并（A6:E6）")
    print("4. 制单人行应该正确显示")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
    print("\n💡 检查要点:")
    print("- 合计行是否跨越了正确的列数")
    print("- 制单人和签收人是否在正确位置")
    print("- 插入的数据行是否影响了下方的合并单元格")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 50)
print("🔧 合并单元格修复测试完成！")
print("请检查浏览器中的显示效果，特别是合计行的合并情况。")
