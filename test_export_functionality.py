import requests
import os

print("📥 测试导出功能")
print("=" * 60)

# 测试Excel导出
print("1. 测试Excel模板导出功能")
excel_data = {
    "data": {
        "list": [
            {"batno": "EXPORT001", "name": "导出测试产品A", "quantity": 15, "amount": 3000},
            {"batno": "EXPORT002", "name": "导出测试产品B", "quantity": 25, "amount": 7500}
        ],
        "name": "导出测试客户",
        "creator": "导出测试员",
        "ccdkh": "EXPORT20250621"
    },
    "template_name": "销售出库单"  # Excel模板
}

try:
    # 先处理Excel模板
    print("   📊 处理Excel模板...")
    response = requests.post("http://localhost:5000/api/print", json=excel_data, timeout=30)
    
    if response.status_code == 200:
        print("   ✅ Excel模板处理成功")
        
        # 检查导出信息
        print("   📋 检查导出信息...")
        info_response = requests.get("http://localhost:5000/api/export/info")
        
        if info_response.status_code == 200:
            info = info_response.json()
            print(f"   📄 可导出文件: {info}")
            
            if info.get('available'):
                print(f"   ✅ Excel文件可导出:")
                print(f"      文件名: {info['filename']}")
                print(f"      类型: {info['type']}")
                print(f"      大小: {info['size_mb']} MB")
                print(f"      修改时间: {info['modified']}")
                
                # 测试实际导出
                print("   📥 测试Excel文件导出...")
                export_response = requests.get("http://localhost:5000/api/export")
                
                if export_response.status_code == 200:
                    print("   ✅ Excel文件导出成功")
                    print(f"   📊 文件大小: {len(export_response.content)} 字节")
                    print(f"   📋 Content-Type: {export_response.headers.get('Content-Type')}")
                    
                    # 验证是否是Excel文件
                    if 'spreadsheetml' in export_response.headers.get('Content-Type', ''):
                        print("   ✅ 确认为Excel文件格式")
                    else:
                        print("   ❌ 文件格式不正确")
                else:
                    print(f"   ❌ Excel导出失败: {export_response.status_code}")
            else:
                print(f"   ❌ Excel文件不可导出: {info.get('message')}")
        else:
            print(f"   ❌ 获取导出信息失败: {info_response.status_code}")
    else:
        print(f"   ❌ Excel模板处理失败: {response.status_code}")

except Exception as e:
    print(f"   ❌ Excel测试失败: {e}")

print("\n" + "-" * 60)

# 测试Word导出
print("2. 测试Word模板导出功能")
word_data = {
    "data": {
        "cdkh": "WORD_EXPORT_001",
        "list": [
            {"name": "Word导出产品A", "quantity": 10, "note": "Word导出备注A"},
            {"name": "Word导出产品B", "quantity": 20, "note": "Word导出备注B"}
        ],
        "creator": "Word导出测试员",
        "date": "2025-06-21"
    },
    "template_name": "委外加工过程不良品记录表"  # Word模板
}

try:
    # 先处理Word模板
    print("   📄 处理Word模板...")
    response = requests.post("http://localhost:5000/api/print", json=word_data, timeout=30)
    
    if response.status_code == 200:
        print("   ✅ Word模板处理成功")
        
        # 检查导出信息
        print("   📋 检查导出信息...")
        info_response = requests.get("http://localhost:5000/api/export/info")
        
        if info_response.status_code == 200:
            info = info_response.json()
            print(f"   📄 可导出文件: {info}")
            
            if info.get('available'):
                print(f"   ✅ Word文件可导出:")
                print(f"      文件名: {info['filename']}")
                print(f"      类型: {info['type']}")
                print(f"      大小: {info['size_mb']} MB")
                print(f"      修改时间: {info['modified']}")
                
                # 测试实际导出
                print("   📥 测试Word文件导出...")
                export_response = requests.get("http://localhost:5000/api/export")
                
                if export_response.status_code == 200:
                    print("   ✅ Word文件导出成功")
                    print(f"   📊 文件大小: {len(export_response.content)} 字节")
                    print(f"   📋 Content-Type: {export_response.headers.get('Content-Type')}")
                    
                    # 验证是否是Word文件
                    if 'wordprocessingml' in export_response.headers.get('Content-Type', ''):
                        print("   ✅ 确认为Word文件格式")
                    else:
                        print("   ❌ 文件格式不正确")
                else:
                    print(f"   ❌ Word导出失败: {export_response.status_code}")
            else:
                print(f"   ❌ Word文件不可导出: {info.get('message')}")
        else:
            print(f"   ❌ 获取导出信息失败: {info_response.status_code}")
    else:
        print(f"   ❌ Word模板处理失败: {response.status_code}")

except Exception as e:
    print(f"   ❌ Word测试失败: {e}")

print("\n" + "=" * 60)
print("📥 导出功能测试完成")

print("\n🎯 功能说明:")
print("✅ Excel模板 → 导出Excel文件 (.xlsx)")
print("✅ Word模板 → 导出Word文件 (.docx)")
print("✅ 自动识别文件类型和MIME类型")
print("✅ 提供文件信息查询API")
print("✅ 支持浏览器下载")

print("\n🔗 使用方式:")
print("1. 处理模板: POST /api/print")
print("2. 查看结果: GET /display")
print("3. 获取导出信息: GET /api/export/info")
print("4. 导出文件: GET /api/export")
print("5. 前端按钮: 点击'导出文件'按钮")

print("\n💡 前端功能:")
print("- 自动检测可导出文件类型")
print("- 显示文件信息确认对话框")
print("- 进度提示和状态反馈")
print("- 自动下载到本地")

print("\n📁 文件位置:")
print("- 填充后的文件保存在: filled_outputs/")
print("- Excel文件: filled_*.xlsx")
print("- Word文件: filled_*.docx")
