from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "样式丰富的模板"

# 定义样式
title_font = Font(name='微软雅黑', size=18, bold=True, color='FF0000')  # 红色标题
header_font = Font(name='宋体', size=12, bold=True, color='FFFFFF')  # 白色字体
normal_font = Font(name='宋体', size=10, color='000000')  # 黑色字体
small_font = Font(name='宋体', size=9, italic=True, color='666666')  # 灰色斜体

# 定义填充色
header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')  # 蓝色背景
alt_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')  # 浅灰色背景

# 定义对齐方式
center_align = Alignment(horizontal='center', vertical='center')
left_align = Alignment(horizontal='left', vertical='center')
right_align = Alignment(horizontal='right', vertical='center')

# 定义边框
thick_border = Border(
    left=Side(style='thick'),
    right=Side(style='thick'),
    top=Side(style='thick'),
    bottom=Side(style='thick')
)

thin_border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 创建标题（合并单元格，大字体，红色）
ws.merge_cells('A1:F1')
ws['A1'] = "{{company_name}} 销售发票"
ws['A1'].font = title_font
ws['A1'].alignment = center_align
ws['A1'].border = thick_border

# 设置行高
ws.row_dimensions[1].height = 40

# 发票信息区域
ws.merge_cells('A3:B3')
ws['A3'] = "发票号码: {{invoice_no}}"
ws['A3'].font = Font(name='宋体', size=11, bold=True)
ws['A3'].alignment = left_align

ws.merge_cells('D3:F3')
ws['D3'] = "开票日期: {{invoice_date}}"
ws['D3'].font = Font(name='宋体', size=11, bold=True)
ws['D3'].alignment = right_align

ws.merge_cells('A4:B4')
ws['A4'] = "客户名称: {{customer_name}}"
ws['A4'].font = normal_font
ws['A4'].alignment = left_align

ws.merge_cells('D4:F4')
ws['D4'] = "客户电话: {{customer_phone}}"
ws['D4'].font = normal_font
ws['D4'].alignment = right_align

# 商品清单表头（蓝色背景，白色字体）
headers = ['序号', '商品名称', '规格', '数量', '单价', '金额']
for i, header in enumerate(headers):
    cell = ws.cell(row=6, column=i+1, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = center_align
    cell.border = thin_border

# 设置表头行高
ws.row_dimensions[6].height = 25

# 添加商品数据行（交替背景色）
for row in range(7, 10):
    # 序号
    cell = ws.cell(row=row, column=1, value=row-6)
    cell.font = normal_font
    cell.alignment = center_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill
    
    # 商品名称
    cell = ws.cell(row=row, column=2, value=f"{{{{items{row-6}_name}}}}")
    cell.font = normal_font
    cell.alignment = left_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill
    
    # 规格
    cell = ws.cell(row=row, column=3, value=f"{{{{items{row-6}_spec}}}}")
    cell.font = small_font
    cell.alignment = left_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill
    
    # 数量
    cell = ws.cell(row=row, column=4, value=f"{{{{items{row-6}_quantity}}}}")
    cell.font = normal_font
    cell.alignment = center_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill
    
    # 单价
    cell = ws.cell(row=row, column=5, value=f"{{{{items{row-6}_price}}}}")
    cell.font = Font(name='Arial', size=10, color='0000FF')  # 蓝色价格
    cell.alignment = right_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill
    
    # 金额
    cell = ws.cell(row=row, column=6, value=f"{{{{items{row-6}_total}}}}")
    cell.font = Font(name='Arial', size=10, bold=True, color='FF0000')  # 红色金额
    cell.alignment = right_align
    cell.border = thin_border
    if row % 2 == 0:
        cell.fill = alt_fill

# 合计行（特殊样式）
ws.merge_cells('A11:E11')
ws['A11'] = "合计金额（大写）: {{total_chinese}}"
ws['A11'].font = Font(name='宋体', size=11, bold=True, color='000080')  # 深蓝色
ws['A11'].alignment = right_align
ws['A11'].border = thick_border
ws['A11'].fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')  # 黄色背景

ws['F11'] = "{{total_amount}}"
ws['F11'].font = Font(name='Arial', size=12, bold=True, color='FF0000')  # 红色大字
ws['F11'].alignment = center_align
ws['F11'].border = thick_border
ws['F11'].fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')

# 备注区域
ws.merge_cells('A13:F13')
ws['A13'] = "备注: {{remarks}}"
ws['A13'].font = Font(name='宋体', size=9, italic=True, color='666666')
ws['A13'].alignment = left_align

# 签名区域
ws.merge_cells('A15:C15')
ws['A15'] = "开票人: {{issuer}}"
ws['A15'].font = normal_font
ws['A15'].alignment = left_align

ws.merge_cells('D15:F15')
ws['D15'] = "复核人: {{reviewer}}"
ws['D15'].font = normal_font
ws['D15'].alignment = right_align

# 设置列宽
column_widths = {
    'A': 8,   # 序号
    'B': 25,  # 商品名称
    'C': 15,  # 规格
    'D': 8,   # 数量
    'E': 12,  # 单价
    'F': 12   # 金额
}

for col, width in column_widths.items():
    ws.column_dimensions[col].width = width

# 设置行高
for row in range(7, 10):
    ws.row_dimensions[row].height = 22

ws.row_dimensions[11].height = 30  # 合计行
ws.row_dimensions[13].height = 20  # 备注行
ws.row_dimensions[15].height = 25  # 签名行

# 保存工作簿
wb.save("templates/styled_template.xlsx")
print("样式丰富的模板已创建: templates/styled_template.xlsx")
