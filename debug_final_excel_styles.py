import requests
import openpyxl
import glob
import os

# 发送测试数据
data = {
    "data": {
        "list": [
            {"batno": "TEST001", "name": "测试商品1", "matrial": "塑料", "unit": "个"},
            {"batno": "TEST002", "name": "测试商品2", "matrial": "金属", "unit": "套"}
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 发送测试数据 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 找到最新生成的Excel文件
    excel_files = glob.glob("filled_outputs/filled_delivery.xlsx_*.xlsx")
    if excel_files:
        latest_file = max(excel_files, key=os.path.getctime)
        print(f"检查最新文件: {latest_file}")
        
        # 加载Excel文件
        wb = openpyxl.load_workbook(latest_file)
        ws = wb.active
        
        print("\n=== 检查Excel文件中的字体颜色 ===")
        
        # 检查关键行的字体颜色
        test_rows = [
            (1, "标题行"),
            (4, "表头行"),
            (5, "第1条数据"),
            (6, "第2条数据"),
            (7, "合计行")
        ]
        
        for row_num, desc in test_rows:
            print(f"\n{desc} (第{row_num}行):")
            
            # 检查前5列
            for col_num in range(1, 6):
                cell = ws.cell(row=row_num, column=col_num)
                value = cell.value if cell.value else "(空)"
                
                print(f"  列{col_num}: '{value}'")
                
                if cell.font and cell.font.color:
                    color = cell.font.color
                    print(f"    字体颜色对象: {color}")
                    
                    if hasattr(color, 'rgb') and color.rgb:
                        print(f"    RGB: {color.rgb}")
                    elif hasattr(color, 'theme') and color.theme is not None:
                        print(f"    主题颜色: theme={color.theme}")
                    else:
                        print(f"    其他颜色类型")
                else:
                    print(f"    ❌ 没有字体颜色信息")
        
        print("\n=== 测试样式提取函数 ===")
        
        def extract_cell_style_debug(cell):
            """调试版本的样式提取"""
            style = {}
            
            # 字体样式
            if cell.font:
                if cell.font.name:
                    style['font-family'] = cell.font.name
                if cell.font.size:
                    style['font-size'] = f"{cell.font.size}pt"
                if cell.font.bold:
                    style['font-weight'] = 'bold'
                if cell.font.italic:
                    style['font-style'] = 'italic'
                if cell.font.color:
                    # 处理颜色值
                    try:
                        color = cell.font.color
                        
                        # 处理RGB颜色
                        if color.rgb and isinstance(color.rgb, str):
                            color_rgb = color.rgb
                            if len(color_rgb) == 8:
                                color_rgb = color_rgb[2:]
                            elif len(color_rgb) == 6:
                                pass
                            else:
                                color_rgb = None

                            if color_rgb and len(color_rgb) == 6:
                                style['color'] = f"#{color_rgb}"
                        
                        # 处理主题颜色
                        elif hasattr(color, 'theme') and color.theme is not None:
                            theme_colors = {
                                0: '#FFFFFF',  # 白色
                                1: '#000000',  # 黑色
                                2: '#1F497D',  # 深蓝色
                                3: '#4F81BD',  # 蓝色
                                4: '#9CBB58',  # 绿色
                                5: '#8064A2',  # 紫色
                                6: '#F79646',  # 橙色
                                7: '#4BACC6',  # 青色
                                8: '#F2F2F2',  # 浅灰色
                                9: '#808080',  # 灰色
                            }
                            
                            if color.theme in theme_colors:
                                style['color'] = theme_colors[color.theme]
                            else:
                                style['color'] = '#000000'
                        
                        else:
                            style['color'] = '#000000'
                            
                    except Exception:
                        style['color'] = '#000000'
                else:
                    style['color'] = '#000000'
            
            # 强制设置白色背景
            style['background-color'] = '#FFFFFF'
            
            return style
        
        # 测试数据行的样式提取
        for row_num in [5, 6]:  # 数据行
            print(f"\n第{row_num}行样式提取结果:")
            for col_num in range(2, 6):  # 数据列
                cell = ws.cell(row=row_num, column=col_num)
                style = extract_cell_style_debug(cell)
                value = cell.value if cell.value else "(空)"
                print(f"  列{col_num} '{value}': {style}")
                
                # 重点检查颜色
                if 'color' in style:
                    color = style['color']
                    if color == '#000000':
                        print(f"    ✅ 黑色字体")
                    elif color == '#FFFFFF':
                        print(f"    ❌ 白色字体 (问题!)")
                    else:
                        print(f"    ⚠️ 其他颜色: {color}")
                else:
                    print(f"    ❌ 没有颜色信息")
    else:
        print("❌ 没有找到Excel文件")
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 测试完成 ===")
