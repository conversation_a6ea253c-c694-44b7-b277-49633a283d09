<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel打印 - delivery.xlsx</title>
    <link rel="stylesheet" href="/static/style.css">
    <style>
        /* 基础样式 */
        body {
            background-color: white;
            color: black;
        }

        .container, main, .print-content {
            background-color: white;
        }

        @media print {
            /* 页面设置 */
            @page {
                margin: 0.5in;
                background: white !important;
                background-color: white !important;
                color: black !important;
                size: A4;
            }

            /* 最强制的白色背景设置 */
            html {
                background: white !important;
                background-color: white !important;
                color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            body {
                background: white !important;
                background-color: white !important;
                color: black !important;
                margin: 0 !important;
                padding: 0 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 隐藏不需要打印的元素 */
            .no-print {
                display: none !important;
            }

            /* 容器样式 */
            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                background-color: white !important;
                box-shadow: none !important;
                border: none !important;
            }

            main {
                background-color: white !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .print-content {
                width: 100% !important;
                background-color: white !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .content {
                background-color: white !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 表格样式 */
            .table {
                border-collapse: collapse !important;
                background-color: white !important;
                width: 100% !important;
                margin: 0 !important;
            }

            .table th, .table td {
                border: 1px solid #000 !important;
                background-color: white !important;
                color: black !important;
                padding: 6px 8px !important;
            }

            /* 移除所有背景色和阴影 */
            * {
                box-shadow: none !important;
                text-shadow: none !important;
                background-image: none !important;
            }

            /* 确保文本颜色 */
            h1, h2, h3, h4, h5, h6, p, span, div {
                color: black !important;
            }

            /* 移除条纹背景 */
            .table-striped tbody tr {
                background-color: white !important;
            }

            /* 强制覆盖所有可能的深色样式 */
            *, *::before, *::after {
                background: white !important;
                background-color: white !important;
                color: black !important;
                border-color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 特别针对表格 */
            table, tbody, thead, tr, td, th {
                background: white !important;
                background-color: white !important;
                color: black !important;
            }

            /* 针对可能的深色模式覆盖 */
            @media (prefers-color-scheme: dark) {
                *, html, body {
                    background: white !important;
                    background-color: white !important;
                    color: black !important;
                    color-scheme: light !important;
                }
            }
        }

        /* 打印按钮样式 */
        .print-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }

        .print-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="no-print">
            <h1>Excel打印 - delivery.xlsx</h1>
            <div class="actions">
                <button onclick="window.print()">打印</button>
                <a href="/print" class="button" style="background-color: #ff9800;">🖨️ 专用打印页面</a>
                <a href="/" class="button">返回首页</a>
            </div>
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px;">
                <strong>💡 打印提示：</strong>如果打印预览显示黑色背景，请点击"专用打印页面"按钮，或确保在浏览器打印设置中勾选"背景图形"选项。
            </div>
        </header>

        <main class="print-content">
            <div class="timestamp no-print">
                <p>生成时间: 20250529134115</p>
            </div>

            <div class="content">
                <table class="table table-striped table-bordered"><colgroup><col style="width: 100px;"><col style="width: 77px;"><col style="width: 101px;"><col><col><col style="width: 84px;"><col><col><col></colgroup><thead><tr><th colspan="9" rowspan="2" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">义通工业（天津）有限公司
送货单</th></tr></thead><tbody><tr></tr><tr><td colspan="2" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">收货单位名称：</td><td colspan="2" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; color: #FF0000; text-align: center; vertical-align: middle; background-color: #FFFFFF">调试客户</td><td colspan="2" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">收货日期：</td><td colspan="3" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td></tr><tr><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">序号</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">批   号</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">名      称</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">材质</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">单位</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">数量</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">单价</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">金额</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">备  注</td></tr><tr><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">DEBUG001</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">调试商品</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">测试材料</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">件</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td></tr><tr><td colspan="5" style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">合        计：</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td></tr><tr><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">制单人：</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; color: #FF0000; text-align: center; vertical-align: middle; background-color: #FFFFFF">调试员</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">送货人：</td><td colspan="2" style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; color: #FF0000; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">签收人：</td><td style="font-family: 宋体; font-size: 11.0pt; font-weight: bold; text-align: center; vertical-align: middle; background-color: #FFFFFF">&nbsp;</td></tr></tbody></table>
            </div>
        </main>

        <footer class="no-print">
            <p>&copy; 2023 模板打印系统</p>
        </footer>
    </div>
</body>
</html>