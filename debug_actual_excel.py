import requests
import openpyxl
import time
import os

# 发送您的原始数据
data = {
    "data": {
        "list": [
            {
                "batno": "a",
                "name": "sdad", 
                "matrial": "wd",
                "unit": "个",
                "quantity": 2,
                "note": None
            },
            {
                "batno": "ds",
                "name": "da",
                "matrial": None,
                "unit": "发a",
                "quantity": 2
            }
        ],
        "name": "测试客户132",
        "creator": "Super Admin"
    },
    "template_name": "delivery"
}

print("=== 发送您的原始数据 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    # 等待处理完成
    time.sleep(1)
    
    # 找到最新生成的Excel文件
    filled_files = []
    if os.path.exists('filled_outputs'):
        filled_files = [f for f in os.listdir('filled_outputs') if f.endswith('.xlsx')]
        filled_files.sort(key=lambda x: os.path.getctime(os.path.join('filled_outputs', x)), reverse=True)
    
    if filled_files:
        latest_file = os.path.join('filled_outputs', filled_files[0])
        print(f"检查最新文件: {latest_file}")
        
        # 加载Excel文件
        wb = openpyxl.load_workbook(latest_file)
        ws = wb.active
        
        print("\n=== 检查生成的Excel文件 ===")
        
        # 检查表头（第4行）
        print("第4行（表头）:")
        headers = []
        for col in range(1, 10):
            cell = ws.cell(row=4, column=col)
            value = cell.value if cell.value else ''
            headers.append(value)
            print(f"  第{col}列: '{value}'")
        
        # 检查数据行（第5行和第6行）
        print("\n第5行（第1条数据）:")
        row_5_data = []
        for col in range(1, 10):
            cell = ws.cell(row=5, column=col)
            value = cell.value if cell.value else ''
            row_5_data.append(value)
            print(f"  第{col}列: '{value}'")
        
        print("\n第6行（第2条数据）:")
        row_6_data = []
        for col in range(1, 10):
            cell = ws.cell(row=6, column=col)
            value = cell.value if cell.value else ''
            row_6_data.append(value)
            print(f"  第{col}列: '{value}'")
        
        print("\n=== 数据验证 ===")
        
        # 期望的数据
        expected_data_1 = {
            1: "",      # 序号（空）
            2: "a",     # 批号
            3: "sdad",  # 名称
            4: "wd",    # 材质
            5: "个",    # 单位
        }
        
        expected_data_2 = {
            1: "",      # 序号（空）
            2: "ds",    # 批号
            3: "da",    # 名称
            4: "",      # 材质（null）
            5: "发a",   # 单位
        }
        
        print("验证第5行数据:")
        all_correct_5 = True
        for col, expected in expected_data_1.items():
            actual = str(row_5_data[col-1]) if row_5_data[col-1] else ""
            expected_str = str(expected) if expected else ""
            header = headers[col-1] if col-1 < len(headers) else f"列{col}"
            
            if actual == expected_str:
                print(f"  ✅ 第{col}列 ({header}): '{actual}'")
            else:
                print(f"  ❌ 第{col}列 ({header}): 期望 '{expected_str}', 实际 '{actual}'")
                all_correct_5 = False
        
        print("\n验证第6行数据:")
        all_correct_6 = True
        for col, expected in expected_data_2.items():
            actual = str(row_6_data[col-1]) if row_6_data[col-1] else ""
            expected_str = str(expected) if expected else ""
            header = headers[col-1] if col-1 < len(headers) else f"列{col}"
            
            if actual == expected_str:
                print(f"  ✅ 第{col}列 ({header}): '{actual}'")
            else:
                print(f"  ❌ 第{col}列 ({header}): 期望 '{expected_str}', 实际 '{actual}'")
                all_correct_6 = False
        
        if all_correct_5 and all_correct_6:
            print("\n🎉 Excel文件中的数据完全正确！")
            print("问题可能在HTML转换过程中")
        else:
            print("\n❌ Excel文件中的数据有误")
            
        # 检查是否有额外的行
        print(f"\n=== 检查总行数 ===")
        print(f"Excel最大行数: {ws.max_row}")
        
        # 检查第7行是否有数据（应该是"合计"行）
        print("\n第7行:")
        for col in range(1, 6):
            cell = ws.cell(row=7, column=col)
            value = cell.value if cell.value else ''
            print(f"  第{col}列: '{value}'")
            
    else:
        print("❌ 没有找到生成的Excel文件")
else:
    print(f"❌ API调用失败: {response.text}")
