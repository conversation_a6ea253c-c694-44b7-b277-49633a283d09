import openpyxl

# 检查模板中的SUM公式
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查模板中的公式 ===")

formulas_found = []
for row in range(1, ws.max_row + 1):
    for col in range(1, ws.max_column + 1):
        cell = ws.cell(row=row, column=col)
        if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
            formulas_found.append({
                'coordinate': cell.coordinate,
                'row': row,
                'col': col,
                'formula': cell.value
            })

if formulas_found:
    print(f"找到 {len(formulas_found)} 个公式:")
    for formula in formulas_found:
        print(f"  {formula['coordinate']}: {formula['formula']}")
else:
    print("没有找到公式")

print("\n=== 检查可能的数值列 ===")
# 检查哪些列可能包含数值，需要求和
headers = []
for col in range(1, ws.max_column + 1):
    header_cell = ws.cell(row=4, column=col)  # 第4行是表头
    if header_cell.value:
        headers.append({
            'col': col,
            'header': header_cell.value,
            'coordinate': header_cell.coordinate
        })

print("表头信息:")
for header in headers:
    print(f"  第{header['col']}列 ({header['coordinate']}): {header['header']}")

# 识别可能需要求和的列
sum_candidates = []
for header in headers:
    header_text = str(header['header']).lower()
    if any(keyword in header_text for keyword in ['数量', '金额', '单价', '总计', '小计', '合计']):
        sum_candidates.append(header)

if sum_candidates:
    print(f"\n可能需要求和的列:")
    for candidate in sum_candidates:
        print(f"  第{candidate['col']}列: {candidate['header']}")
else:
    print("\n没有发现明显需要求和的列")

print("\n=== 建议的SUM公式处理方案 ===")
print("1. 自动检测数值列")
print("2. 在合计行添加SUM公式")
print("3. 动态调整公式范围")
print("4. 支持多种求和场景")

# 创建一个包含SUM公式的示例模板
print("\n=== 创建包含SUM公式的示例模板 ===")

# 复制原模板
wb_with_sum = openpyxl.load_workbook('templates/delivery.xlsx')
ws_sum = wb_with_sum.active

# 在数量列（第6列）和金额列（第8列）的合计行添加SUM公式
# 假设数据从第5行开始，合计行在第6行（原始位置）

# 数量列求和（第6列）
quantity_sum_cell = ws_sum.cell(row=6, column=6)
quantity_sum_cell.value = "=SUM(F5:F5)"  # 初始范围，会动态调整

# 金额列求和（第8列）
amount_sum_cell = ws_sum.cell(row=6, column=8)
amount_sum_cell.value = "=SUM(H5:H5)"  # 初始范围，会动态调整

# 保存带SUM公式的模板
sum_template_path = 'templates/delivery_with_sum.xlsx'
wb_with_sum.save(sum_template_path)
print(f"✅ 创建了包含SUM公式的模板: {sum_template_path}")

print("\n=== 检查新模板中的公式 ===")
wb_verify = openpyxl.load_workbook(sum_template_path)
ws_verify = wb_verify.active

for row in range(1, ws_verify.max_row + 1):
    for col in range(1, ws_verify.max_column + 1):
        cell = ws_verify.cell(row=row, column=col)
        if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
            print(f"  {cell.coordinate}: {cell.value}")

print("\n=== 检查完成 ===")
