<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        /* 基础样式 */
        body {
            background-color: white;
            color: black;
        }

        .container, main, .print-content {
            background-color: white;
        }

        @media print {
            /* 页面设置 */
            @page {
                margin: 0.5in;
                background: white !important;
                background-color: white !important;
                color: black !important;
                size: A4;
            }

            /* 最强制的白色背景设置 */
            html {
                background: white !important;
                background-color: white !important;
                color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            body {
                background: white !important;
                background-color: white !important;
                color: black !important;
                margin: 0 !important;
                padding: 0 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 隐藏不需要打印的元素 */
            .no-print {
                display: none !important;
            }

            /* 容器样式 */
            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                background-color: white !important;
                box-shadow: none !important;
                border: none !important;
            }

            main {
                background-color: white !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .print-content {
                width: 100% !important;
                background-color: white !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .content {
                background-color: white !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 表格样式 */
            .table {
                border-collapse: collapse !important;
                background-color: white !important;
                width: 100% !important;
                margin: 0 !important;
            }

            .table th, .table td {
                border: 1px solid #000 !important;
                background-color: white !important;
                padding: 6px 8px !important;
                /* 字体颜色由内联样式控制，不在这里强制设置 */
            }

            /* 移除所有背景色和阴影 */
            * {
                box-shadow: none !important;
                text-shadow: none !important;
                background-image: none !important;
            }

            /* 确保文本颜色 */
            h1, h2, h3, h4, h5, h6, p, span, div {
                color: black !important;
            }

            /* 移除条纹背景 */
            .table-striped tbody tr {
                background-color: white !important;
            }

            /* 强制覆盖所有可能的深色样式，但不覆盖内联颜色样式 */
            *, *::before, *::after {
                background: white !important;
                background-color: white !important;
                border-color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 为没有内联样式的元素设置默认颜色 */
            body, div, p, span, h1, h2, h3, h4, h5, h6 {
                color: black;
            }

            /* 表格单元格保持内联样式优先级 */
            table td[style], table th[style] {
                /* 内联样式会自动覆盖这里的设置 */
            }

            /* 强制确保红色字体显示 */
            table td[style*="color: #FF0000"], table th[style*="color: #FF0000"] {
                color: #FF0000 !important;
            }

            /* 强制确保红色字体显示（小写） */
            table td[style*="color: #ff0000"], table th[style*="color: #ff0000"] {
                color: #FF0000 !important;
            }

            /* 特别针对表格背景，但不强制字体颜色 */
            table, tbody, thead, tr {
                background: white !important;
                background-color: white !important;
            }

            /* 表格单元格背景，字体颜色由内联样式控制 */
            td, th {
                background: white !important;
                background-color: white !important;
            }

            /* 针对可能的深色模式覆盖 */
            @media (prefers-color-scheme: dark) {
                html, body {
                    background: white !important;
                    background-color: white !important;
                    color: black !important;
                    color-scheme: light !important;
                }
            }
        }

        /* 打印按钮样式 */
        .print-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }

        .print-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="no-print">
            <h1>{{ title }}</h1>
            <div class="actions">
                <button onclick="window.print()">打印</button>
                <a href="/print" class="button" style="background-color: #ff9800;">🖨️ 专用打印页面</a>
                <a href="/" class="button">返回首页</a>
            </div>
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px;">
                <strong>💡 打印提示：</strong>如果打印预览显示黑色背景，请点击"专用打印页面"按钮，或确保在浏览器打印设置中勾选"背景图形"选项。
            </div>
        </header>

        <main class="print-content">
            <div class="timestamp no-print">
                <p>生成时间: {{ timestamp }}</p>
            </div>

            <div class="content">
                {{ content|safe }}
            </div>
        </main>

        <footer class="no-print">
            <p>&copy; 2023 模板打印系统</p>
        </footer>
    </div>
</body>
</html>
