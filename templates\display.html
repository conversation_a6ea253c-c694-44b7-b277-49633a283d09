<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        /* 基础样式 */
        body {
            background-color: white;
            color: black;
        }

        .container, main, .print-content {
            background-color: white;
        }

        @media print {
            /* 页面设置 */
            @page {
                margin: 0.5in;
                background: white !important;
                background-color: white !important;
                color: black !important;
                size: A4;
            }

            /* 最强制的白色背景设置 */
            html {
                background: white !important;
                background-color: white !important;
                color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            body {
                background: white !important;
                background-color: white !important;
                color: black !important;
                margin: 0 !important;
                padding: 0 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 隐藏不需要打印的元素 */
            .no-print {
                display: none !important;
            }

            /* 容器样式 */
            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                background-color: white !important;
                box-shadow: none !important;
                border: none !important;
            }

            main {
                background-color: white !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .print-content {
                width: 100% !important;
                background-color: white !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .content {
                background-color: white !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 表格样式 */
            .table {
                border-collapse: collapse !important;
                background-color: white !important;
                width: 100% !important;
                margin: 0 !important;
            }

            .table th, .table td {
                border: 1px solid #000 !important;
                background-color: white !important;
                padding: 6px 8px !important;
                /* 字体颜色由内联样式控制，不在这里强制设置 */
            }

            /* 移除所有背景色和阴影 */
            * {
                box-shadow: none !important;
                text-shadow: none !important;
                background-image: none !important;
            }

            /* 确保文本颜色 */
            h1, h2, h3, h4, h5, h6, p, span, div {
                color: black !important;
            }

            /* 移除条纹背景 */
            .table-striped tbody tr {
                background-color: white !important;
            }

            /* 强制覆盖所有可能的深色样式，但不覆盖内联颜色样式 */
            *, *::before, *::after {
                background: white !important;
                background-color: white !important;
                border-color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            /* 为没有内联样式的元素设置默认颜色 */
            body, div, p, span, h1, h2, h3, h4, h5, h6 {
                color: black;
            }

            /* 表格单元格保持内联样式优先级 */
            table td[style], table th[style] {
                /* 内联样式会自动覆盖这里的设置 */
            }

            /* 强制确保红色字体显示 */
            table td[style*="color: #FF0000"], table th[style*="color: #FF0000"] {
                color: #FF0000 !important;
            }

            /* 强制确保红色字体显示（小写） */
            table td[style*="color: #ff0000"], table th[style*="color: #ff0000"] {
                color: #FF0000 !important;
            }

            /* 特别针对表格背景，但不强制字体颜色 */
            table, tbody, thead, tr {
                background: white !important;
                background-color: white !important;
            }

            /* 表格单元格背景，字体颜色由内联样式控制 */
            td, th {
                background: white !important;
                background-color: white !important;
            }

            /* 针对可能的深色模式覆盖 */
            @media (prefers-color-scheme: dark) {
                html, body {
                    background: white !important;
                    background-color: white !important;
                    color: black !important;
                    color-scheme: light !important;
                }
            }
        }

        /* 打印按钮样式 */
        .print-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }

        .print-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="no-print">
            <h1>{{ title }}</h1>
            <div class="actions">
                <button onclick="window.print()">打印</button>
                <a href="/print" class="button" style="background-color: #ff9800;">🖨️ 专用打印页面</a>
                <button id="exportBtn" onclick="exportFile()" style="background-color: #2196F3; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 0 5px;">📥 导出文件</button>
                <a href="/" class="button">返回首页</a>
            </div>
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px;">
                <strong>💡 打印提示：</strong>如果打印预览显示黑色背景，请点击"专用打印页面"按钮，或确保在浏览器打印设置中勾选"背景图形"选项。
            </div>
        </header>

        <main class="print-content">
            <div class="timestamp no-print">
                <p>生成时间: {{ timestamp }}</p>
            </div>

            <div class="content">
                {{ content|safe }}
            </div>
        </main>

        <footer class="no-print">
            <p>&copy; 2023 模板打印系统</p>
        </footer>
    </div>

    <script>
        // 导出文件功能
        async function exportFile() {
            const exportBtn = document.getElementById('exportBtn');
            const originalText = exportBtn.innerHTML;

            try {
                // 显示加载状态
                exportBtn.innerHTML = '⏳ 准备导出...';
                exportBtn.disabled = true;

                // 首先获取导出信息
                const infoResponse = await fetch('/api/export/info');
                const info = await infoResponse.json();

                if (!info.available) {
                    alert('没有可导出的文件：' + info.message);
                    return;
                }

                // 显示文件信息并确认导出
                const fileType = info.type;
                const fileName = info.filename;
                const fileSize = info.size_mb;

                const confirmMessage = `准备导出${fileType}文件：\n\n` +
                    `文件名：${fileName}\n` +
                    `类型：${fileType}文档\n` +
                    `大小：${fileSize} MB\n` +
                    `修改时间：${info.modified}\n\n` +
                    `是否确认导出？`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                // 更新按钮状态
                exportBtn.innerHTML = `📥 导出${fileType}中...`;

                // 执行导出
                const exportResponse = await fetch('/api/export');

                if (exportResponse.ok) {
                    // 创建下载链接
                    const blob = await exportResponse.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // 显示成功消息
                    exportBtn.innerHTML = '✅ 导出成功';
                    setTimeout(() => {
                        exportBtn.innerHTML = originalText;
                    }, 2000);

                    // 显示成功提示
                    alert(`${fileType}文件导出成功！\n文件名：${fileName}`);
                } else {
                    const error = await exportResponse.json();
                    throw new Error(error.error || '导出失败');
                }

            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败：' + error.message);
                exportBtn.innerHTML = '❌ 导出失败';
                setTimeout(() => {
                    exportBtn.innerHTML = originalText;
                }, 2000);
            } finally {
                exportBtn.disabled = false;
            }
        }

        // 页面加载时检查导出状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/api/export/info');
                const info = await response.json();

                const exportBtn = document.getElementById('exportBtn');
                if (info.available) {
                    exportBtn.title = `导出${info.type}文件：${info.filename}`;
                    exportBtn.innerHTML = `📥 导出${info.type}`;
                } else {
                    exportBtn.disabled = true;
                    exportBtn.title = '没有可导出的文件';
                    exportBtn.innerHTML = '📥 无文件可导出';
                }
            } catch (error) {
                console.error('检查导出状态失败:', error);
            }
        });
    </script>
</body>
</html>
