import sys
import os

# 添加当前目录到Python路径
sys.path.append('.')

# 直接导入和测试Word处理函数
try:
    from app import process_word_template
    
    print("=== 直接测试Word处理函数 ===")
    
    # 测试数据
    test_data = {
        "cdkh": "DIRECT_TEST_001",
        "list": [
            {"name": "测试产品A", "quantity": 10, "note": "测试备注A"},
            {"name": "测试产品B", "quantity": 20, "note": "测试备注B"}
        ],
        "creator": "直接测试员",
        "date": "2025-06-20"
    }
    
    # 模板路径
    template_path = "templates/委外加工过程不良品记录表.docx"
    
    print(f"模板路径: {template_path}")
    print(f"模板存在: {os.path.exists(template_path)}")
    print(f"测试数据: {test_data}")
    
    # 调用处理函数
    print("\n开始处理Word模板...")
    result = process_word_template(template_path, test_data)
    
    print(f"\n处理结果: {result}")
    
    if "success" in result and result["success"]:
        print("✅ Word模板处理成功！")
        print(f"输出文件: {result.get('output_path', '未知')}")
        
        # 检查输出文件是否存在
        output_path = result.get('output_path')
        if output_path and os.path.exists(output_path):
            print(f"✅ 输出文件存在: {output_path}")
            file_size = os.path.getsize(output_path)
            print(f"文件大小: {file_size} 字节")
        else:
            print(f"❌ 输出文件不存在: {output_path}")
    else:
        print(f"❌ Word模板处理失败: {result}")

except Exception as e:
    print(f"❌ 直接测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 直接测试完成 ===")
