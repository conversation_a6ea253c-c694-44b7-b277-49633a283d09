import openpyxl
import glob
import os

# 找到最新生成的Excel文件
excel_files = glob.glob("filled_outputs/filled_delivery.xlsx_*.xlsx")
if excel_files:
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"检查最新文件: {latest_file}")
    
    # 加载Excel文件
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 验证自增序号功能 ===")
    
    # 检查数据行的序号
    data_rows = [5, 6, 7, 8]  # 数据行
    expected_indexes = [1, 2, 3, 4]  # 期望的序号
    
    print("序号列验证:")
    for i, row_num in enumerate(data_rows):
        # 检查第1列（序号列）
        index_cell = ws.cell(row=row_num, column=1)
        index_value = index_cell.value
        expected_index = expected_indexes[i]
        
        # 检查第2列（批号列）
        batno_cell = ws.cell(row=row_num, column=2)
        batno_value = batno_cell.value
        
        # 检查第3列（名称列）
        name_cell = ws.cell(row=row_num, column=3)
        name_value = name_cell.value
        
        print(f"  第{row_num}行: 序号={index_value}, 批号={batno_value}, 名称={name_value}")
        
        if index_value == expected_index:
            print(f"    ✅ 序号正确: {index_value}")
        else:
            print(f"    ❌ 序号错误: 期望{expected_index}, 实际{index_value}")
    
    print("\n=== 完整表格内容 ===")
    print("行号 | 序号 | 批号     | 名称         | 材质 | 单位")
    print("-" * 50)
    
    for row_num in data_rows:
        row_data = []
        for col_num in range(1, 6):  # 前5列
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value is not None else "(空)"
            row_data.append(str(value))
        
        print(f" {row_num}   | {' | '.join(row_data)}")
    
    print("\n=== 验证结果 ===")
    
    # 验证序号是否连续递增
    all_correct = True
    for i, row_num in enumerate(data_rows):
        index_cell = ws.cell(row=row_num, column=1)
        index_value = index_cell.value
        expected_index = i + 1
        
        if index_value != expected_index:
            all_correct = False
            break
    
    if all_correct:
        print("✅ 自增序号功能完全正确！")
        print("   - 序号从1开始")
        print("   - 序号连续递增")
        print("   - 序号与数据行对应")
    else:
        print("❌ 自增序号功能有问题")
    
    print(f"\n📁 Excel文件位置: {latest_file}")
    
else:
    print("❌ 没有找到Excel文件")

print("\n=== 验证完成 ===")
