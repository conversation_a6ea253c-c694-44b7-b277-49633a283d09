# 动态列表模板使用指南

## 概述

本系统支持处理动态数量的列表数据，特别适合处理来自数据库或API的复杂数据结构。

## 数据格式

您的数据格式：
```json
{
  "template_name": "delivery",
  "data": {
    "list": [
      {
        "id": 1,
        "batno": "a",
        "name": "sdad",
        "matrial": "wd",
        "unit": "个",
        "quantity": 2,
        "note": null,
        "delivery_f": 1
      },
      // ... 更多列表项
    ],
    "name": "测试客户132",
    "creator": "Super Admin"
  }
}
```

## 模板设计

### 1. 基本信息区域
```
A1: 发货单 (标题)
A3: 客户名称: {{name}}
E3: 制单人: {{creator}}
```

### 2. 动态列表区域
```
A7: {{list:list}}           # 动态列表标记
A8: {{item.batno}}          # 列表项模板行
B8: {{item.name}}
C8: {{item.matrial}}
D8: {{item.unit}}
E8: {{item.quantity}}
F8: {{item.note}}
G8: {{item.delivery_f}}
```

### 3. 列表后内容
```
A10: 发货说明:
A11: {{delivery_note}}
A13: 发货人签名: ________________
```

## 关键语法

### 动态列表标记
- `{{list:字段名}}` - 标记动态列表的插入位置
- 例如：`{{list:list}}` 对应数据中的 `"list"` 字段

### 列表项占位符
- `{{item.字段名}}` - 引用列表项中的字段
- 例如：`{{item.name}}` 对应列表项中的 `"name"` 字段

## 工作原理

1. **扫描模板**：系统扫描Excel模板，查找 `{{list:xxx}}` 标记
2. **定位模板行**：在标记的下一行查找 `{{item.xxx}}` 占位符
3. **动态插入**：根据列表数据动态插入行
4. **样式保持**：保持模板行的样式和格式
5. **内容调整**：自动调整列表后的内容位置

## 特性支持

### ✅ 支持的功能
- 任意数量的列表项
- 复杂的数据结构
- null/None值自动处理为空
- 保持Excel样式和格式
- 列表下方内容自动调整
- 合并单元格支持
- 字体、颜色、对齐方式保持

### 📋 模板设计建议

1. **表头设计**
   ```
   A7: 批次号  B7: 名称  C7: 材质  D7: 单位  E7: 数量
   ```

2. **模板行设计**
   ```
   A8: {{item.batno}}  B8: {{item.name}}  C8: {{item.matrial}}
   ```

3. **样式设置**
   - 为表头设置背景色和粗体
   - 为模板行设置边框和对齐方式
   - 设置合适的列宽和行高

## 实际应用示例

### 发货单模板
- 客户信息（固定）
- 商品列表（动态）
- 签名区域（固定）

### 订单模板
- 订单头信息（固定）
- 订单明细（动态）
- 合计信息（固定）

### 报表模板
- 报表标题（固定）
- 数据明细（动态）
- 统计汇总（固定）

## 注意事项

1. **字段映射**：确保 `{{item.字段名}}` 与数据中的字段名完全匹配
2. **null值处理**：null值会自动显示为空，不会显示"null"
3. **数据类型**：数字、字符串、日期都会自动转换为文本显示
4. **样式继承**：动态插入的行会继承模板行的样式

## 错误排查

### 常见问题
1. **列表不显示**：检查 `{{list:字段名}}` 是否正确
2. **字段为空**：检查 `{{item.字段名}}` 拼写是否正确
3. **样式丢失**：确保模板行设置了正确的样式
4. **位置错误**：确保列表标记和模板行的位置关系正确

### 调试方法
1. 检查API响应是否成功
2. 查看系统日志文件
3. 验证JSON数据格式
4. 确认模板文件路径正确

## 总结

这种动态列表功能让您可以：
- 处理任意数量的数据项
- 保持专业的Excel格式
- 自动适应不同的数据量
- 支持复杂的业务场景

非常适合处理来自数据库、API或其他系统的动态数据！
