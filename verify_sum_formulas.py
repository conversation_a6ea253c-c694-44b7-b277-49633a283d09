import openpyxl
import glob
import os

# 找到最新生成的Excel文件
excel_files = glob.glob("filled_outputs/filled_delivery_with_sum.xlsx_*.xlsx")
if excel_files:
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"检查最新文件: {latest_file}")
    
    # 加载Excel文件
    wb = openpyxl.load_workbook(latest_file)
    ws = wb.active
    
    print("\n=== 验证SUM公式更新 ===")
    
    # 检查所有公式
    formulas_found = []
    for row in range(1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
                formulas_found.append({
                    'coordinate': cell.coordinate,
                    'row': row,
                    'col': col,
                    'formula': cell.value
                })
    
    if formulas_found:
        print(f"找到 {len(formulas_found)} 个公式:")
        for formula in formulas_found:
            print(f"  {formula['coordinate']}: {formula['formula']}")
            
            # 检查是否是SUM公式
            if 'SUM(' in formula['formula'].upper():
                print(f"    ✅ SUM公式检测到")
                
                # 检查范围是否正确更新
                if 'F5:F8' in formula['formula'] or 'H5:H8' in formula['formula']:
                    print(f"    ✅ 公式范围正确更新")
                elif 'F5:F5' in formula['formula'] or 'H5:H5' in formula['formula']:
                    print(f"    ❌ 公式范围未更新")
                else:
                    print(f"    ⚠️ 公式范围未知")
    else:
        print("❌ 没有找到公式")
    
    print("\n=== 检查数据内容 ===")
    print("数据行内容:")
    for row_num in range(5, 9):  # 检查第5-8行（数据行）
        row_data = []
        for col_num in range(1, 7):  # 前6列
            cell = ws.cell(row=row_num, column=col_num)
            value = cell.value if cell.value is not None else "(空)"
            row_data.append(str(value))
        print(f"  第{row_num}行: {' | '.join(row_data)}")
    
    print("\n=== 检查合计行 ===")
    total_row = 9  # 合计行应该在第9行
    
    print(f"合计行内容:")
    row_data = []
    for col_num in range(1, 10):  # 所有列
        cell = ws.cell(row=total_row, column=col_num)
        value = cell.value if cell.value is not None else "(空)"
        if isinstance(value, str) and value.startswith('='):
            row_data.append(f"公式:{value}")
        else:
            row_data.append(str(value))
    print(f"  第{total_row}行: {' | '.join(row_data)}")
    
    print("\n=== 验证结果总结 ===")
    
    # 统计验证结果
    checks = []
    
    # 检查1: 是否找到公式
    if formulas_found:
        checks.append("✅ 找到公式")
    else:
        checks.append("❌ 没有找到公式")
    
    # 检查2: SUM公式是否正确更新
    sum_formulas_updated = False
    for formula in formulas_found:
        if 'SUM(' in formula['formula'].upper():
            if 'F5:F8' in formula['formula'] or 'H5:H8' in formula['formula']:
                sum_formulas_updated = True
                break
    
    if sum_formulas_updated:
        checks.append("✅ SUM公式正确更新")
    else:
        checks.append("❌ SUM公式未正确更新")
    
    # 检查3: 数据行数量
    data_rows_count = 4
    actual_data_rows = 0
    for row_num in range(5, 9):
        cell = ws.cell(row=row_num, column=1)
        if cell.value and str(cell.value).isdigit():
            actual_data_rows += 1
    
    if actual_data_rows == data_rows_count:
        checks.append("✅ 数据行数量正确")
    else:
        checks.append(f"❌ 数据行数量错误: 期望{data_rows_count}, 实际{actual_data_rows}")
    
    # 输出结果
    for check in checks:
        print(check)
    
    success_count = sum(1 for check in checks if check.startswith("✅"))
    total_count = len(checks)
    
    if success_count == total_count:
        print(f"\n🎉 SUM公式功能完全成功！({success_count}/{total_count})")
    else:
        print(f"\n⚠️ 部分功能需要改进 ({success_count}/{total_count})")
    
    print(f"\n📁 Excel文件位置: {latest_file}")
    
    # 详细显示所有公式
    if formulas_found:
        print(f"\n📊 详细公式信息:")
        for formula in formulas_found:
            print(f"  位置: {formula['coordinate']}")
            print(f"  公式: {formula['formula']}")
            print(f"  行列: 第{formula['row']}行第{formula['col']}列")
            print()
    
else:
    print("❌ 没有找到delivery_with_sum的Excel文件")
    
    # 检查是否有其他Excel文件
    all_excel_files = glob.glob("filled_outputs/filled_*.xlsx")
    if all_excel_files:
        print("\n找到的其他Excel文件:")
        for file in all_excel_files[-3:]:  # 显示最新的3个文件
            print(f"  {file}")

print("\n=== 验证完成 ===")
