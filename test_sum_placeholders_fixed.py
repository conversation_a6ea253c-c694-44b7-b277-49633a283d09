import requests

# 使用正确字段名测试 {{sum.field}} 占位符功能
data = {
    "data": {
        "list": [
            {"batno": "FIX001", "name": "修复测试产品1", "matrial": "材料1", "unit": "个", "quantity": 15, "price": 120, "amount": 1800, "note": "修复测试1"},
            {"batno": "FIX002", "name": "修复测试产品2", "matrial": "材料2", "unit": "套", "quantity": 30, "price": 250, "amount": 7500, "note": "修复测试2"},
            {"batno": "FIX003", "name": "修复测试产品3", "matrial": "材料3", "unit": "件", "quantity": 12, "price": 180, "amount": 2160, "note": "修复测试3"}
        ],
        "name": "SUM占位符修复测试客户",
        "creator": "修复测试员"
    },
    "template_name": "delivery_with_sum_placeholders"  # 使用修复后的模板
}

print("🔧 使用正确字段名测试 {{sum.field}} 占位符")
print("=" * 60)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 修复内容:")
    print("- 字段名修正: qty -> quantity, remark -> note")
    print("- SUM占位符修正: {{sum.qty}} -> {{sum.quantity}}")
    print("- 数据字段名与模板完全匹配")
    
    print("\n🎯 正确的字段映射:")
    print("{{list.batno}} -> batno")
    print("{{list.name}} -> name")
    print("{{list.matrial}} -> matrial")
    print("{{list.unit}} -> unit")
    print("{{list.quantity}} -> quantity ✅")
    print("{{list.price}} -> price")
    print("{{list.amount}} -> amount ✅")
    print("{{list.note}} -> note")
    
    print("\n📊 计算验证:")
    print("数量求和: 15 + 30 + 12 = 57")
    print("金额求和: 1800 + 7500 + 2160 = 11460")
    
    print("\n🎨 预期显示效果:")
    print("序号 | 批号   | 名称         | 材质  | 单位 | 数量 | 单价 | 金额 | 备注")
    print("-" * 75)
    print(" 1   | FIX001 | 修复测试产品1 | 材料1 | 个   | 15   | 120  | 1800 | 修复测试1")
    print(" 2   | FIX002 | 修复测试产品2 | 材料2 | 套   | 30   | 250  | 7500 | 修复测试2")
    print(" 3   | FIX003 | 修复测试产品3 | 材料3 | 件   | 12   | 180  | 2160 | 修复测试3")
    print("合计：                                        | 57   |      | 11460|")
    
    print("\n✨ {{sum.field}} 占位符优势:")
    print("🎯 动态适应: 无论list有多少项，都能正确求和")
    print("🔧 简单易用: 只需 {{sum.字段名}} 即可")
    print("📊 智能计算: 自动处理数值类型转换")
    print("🛡️ 错误容忍: 跳过无效数据，不会报错")
    print("🎨 灵活位置: 可以放在模板的任意位置")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
    print("\n💡 使用技巧:")
    print("1. 确保字段名与模板中的占位符完全匹配")
    print("2. SUM占位符只在合计行显示，数据行自动为空")
    print("3. 支持整数、浮点数和数字字符串")
    print("4. 可以在同一模板中使用多个SUM占位符")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 60)
print("🔧 修复后的 {{sum.field}} 占位符测试完成！")
print("现在字段名完全匹配，应该能正确计算SUM值了！")
