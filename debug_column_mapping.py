import openpyxl
import re

# 检查列模板映射
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查第5行的占位符映射 ===")
template_row = 5

column_templates = {}
for col_idx in range(1, ws.max_column + 1):
    template_cell = ws.cell(row=template_row, column=col_idx)
    if template_cell.value and isinstance(template_cell.value, str):
        print(f"第{col_idx}列: '{template_cell.value}'")
        
        # 查找形如 {{item.field}} 或 {{list.field}} 的占位符
        item_matches = re.findall(r'\{\{item\.(\w+)\}\}', template_cell.value)
        list_matches = re.findall(r'\{\{list\.(\w+)\}\}', template_cell.value)
        
        if item_matches:
            column_templates[col_idx] = {
                'field': item_matches[0],
                'template': template_cell.value
            }
            print(f"  -> 映射到字段: {item_matches[0]}")
        elif list_matches:
            column_templates[col_idx] = {
                'field': list_matches[0],
                'template': template_cell.value
            }
            print(f"  -> 映射到字段: {list_matches[0]}")
        else:
            print(f"  -> 未识别的占位符")

print(f"\n=== 列模板映射结果 ===")
for col_idx, template_info in column_templates.items():
    print(f"第{col_idx}列 -> {template_info['field']}")

print(f"\n=== 测试数据映射 ===")
test_data = {
    "batno": "a",
    "name": "sdad",
    "matrial": "wd", 
    "unit": "个"
}

print("期望的列映射:")
print("第2列(B) -> batno: a")
print("第3列(C) -> name: sdad") 
print("第4列(D) -> matrial: wd")
print("第5列(E) -> unit: 个")

print("\n实际的列映射:")
for col_idx, template_info in column_templates.items():
    field = template_info['field']
    value = test_data.get(field, '(未找到)')
    print(f"第{col_idx}列 -> {field}: {value}")

print("\n=== 问题分析 ===")
expected_mapping = {2: 'batno', 3: 'name', 4: 'matrial', 5: 'unit'}
actual_mapping = {col: info['field'] for col, info in column_templates.items()}

if expected_mapping == actual_mapping:
    print("✅ 列映射正确")
else:
    print("❌ 列映射错误")
    print(f"期望: {expected_mapping}")
    print(f"实际: {actual_mapping}")
    
    # 找出差异
    for col in range(1, 10):
        expected = expected_mapping.get(col, '(无)')
        actual = actual_mapping.get(col, '(无)')
        if expected != actual:
            print(f"  第{col}列: 期望 {expected}, 实际 {actual}")

print("\n=== 建议修复 ===")
print("如果列映射错误，需要检查:")
print("1. 模板文件中占位符的位置")
print("2. 列模板检测逻辑")
print("3. 数据填充逻辑")
