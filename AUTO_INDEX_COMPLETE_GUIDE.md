# 自增序号功能完整指南

## 🎯 功能概述

模板打印系统现在支持**两种方式**实现自动生成递增序号功能，为动态列表中的每一行自动添加从1开始的序号。

## 🔧 两种实现方式

### 方式1：自动检测（向后兼容）
- **原理**：系统自动检测第1列是否为空
- **适用场景**：简单的序号需求，第1列就是序号列
- **优点**：无需修改模板，完全自动
- **缺点**：只能在第1列，格式固定

### 方式2：{{auto_index}} 占位符（推荐）
- **原理**：在模板中明确放置 `{{auto_index}}` 占位符
- **适用场景**：需要灵活控制序号位置和格式
- **优点**：灵活位置，支持文本嵌入，可多次使用
- **缺点**：需要修改模板

## 📋 方式1：自动检测

### 使用条件
- 第1列为空或只包含空白字符
- 系统自动识别为序号列

### 模板结构
```
| 序号 | 批号 | 名称 | 材质 | 单位 |
|------|------|------|------|------|
| (空) | {{list.batno}} | {{list.name}} | {{list.matrial}} | {{list.unit}} |
```

### JSON数据
```json
{
  "template_name": "delivery",
  "data": {
    "list": [
      {"batno": "A001", "name": "产品A", "matrial": "材料A", "unit": "个"},
      {"batno": "B002", "name": "产品B", "matrial": "材料B", "unit": "套"}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
```

### 生成结果
```
| 序号 | 批号 | 名称   | 材质   | 单位 |
|------|------|--------|--------|------|
|  1   | A001 | 产品A  | 材料A  | 个   |
|  2   | B002 | 产品B  | 材料B  | 套   |
```

## 🎯 方式2：{{auto_index}} 占位符

### 基本用法

#### 纯序号
```
{{auto_index}}  →  1, 2, 3, 4...
```

#### 嵌入文本
```
第{{auto_index}}项  →  第1项, 第2项, 第3项...
序号:{{auto_index}}  →  序号:1, 序号:2, 序号:3...
```

### 模板结构示例
```
| 序号 | 编号 | 商品名称 | 规格 | 单位 | 备注 |
|------|------|----------|------|------|------|
| {{auto_index}} | {{list.code}} | {{list.name}} | {{list.spec}} | {{list.unit}} | 第{{auto_index}}项 |
```

### JSON数据
```json
{
  "template_name": "flexible_auto_index",
  "data": {
    "list": [
      {"code": "P001", "name": "产品A", "spec": "规格A", "unit": "个"},
      {"code": "P002", "name": "产品B", "spec": "规格B", "unit": "套"}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
```

### 生成结果
```
| 序号 | 编号 | 商品名称 | 规格   | 单位 | 备注   |
|------|------|----------|--------|------|--------|
|  1   | P001 | 产品A   | 规格A  | 个   | 第1项  |
|  2   | P002 | 产品B   | 规格B  | 套   | 第2项  |
```

## ✨ 高级用法

### 多个序号列
```
| 序号 | 项目编号 | 名称 | 备注 |
|------|----------|------|------|
| {{auto_index}} | P{{auto_index}} | {{list.name}} | 第{{auto_index}}个项目 |
```

**结果**：
```
| 序号 | 项目编号 | 名称   | 备注       |
|------|----------|--------|------------|
|  1   | P1      | 产品A  | 第1个项目  |
|  2   | P2      | 产品B  | 第2个项目  |
```

### 不同位置的序号
```
| 名称 | 序号 | 编号 | 描述 |
|------|------|------|------|
| {{list.name}} | {{auto_index}} | {{list.code}} | 这是第{{auto_index}}个产品 |
```

## 🎨 样式说明

### 样式继承
- 序号会继承模板单元格的所有样式：
  - 字体名称和大小
  - 字体颜色（黑色/红色等）
  - 对齐方式（居中/左对齐等）
  - 边框样式
  - 背景颜色

### 显示效果
- **Excel文件**：序号正确显示在指定位置
- **HTML显示**：序号在网页中正确显示
- **打印效果**：序号在打印时清晰可见

## 🔄 兼容性

### 向后兼容
- 两种方式可以同时存在
- 现有模板无需修改即可使用自动检测
- 新模板可以使用 `{{auto_index}}` 占位符

### 优先级
1. 如果模板中有 `{{auto_index}}` 占位符，优先使用
2. 如果第1列为空，启用自动检测
3. 两种方式可以在同一个模板中共存

## 📝 使用建议

### 选择方式1（自动检测）的场景
- ✅ 简单的序号需求
- ✅ 序号固定在第1列
- ✅ 不需要修改现有模板
- ✅ 快速实现序号功能

### 选择方式2（{{auto_index}}）的场景
- ✅ 需要灵活控制序号位置
- ✅ 需要在文本中嵌入序号
- ✅ 需要多个序号列
- ✅ 需要自定义序号格式
- ✅ 新建模板时推荐使用

## 🚀 实际应用示例

### 送货单模板
```
| 序号 | 批号 | 商品名称 | 材质 | 单位 | 备注 |
|------|------|----------|------|------|------|
| {{auto_index}} | {{list.batno}} | {{list.name}} | {{list.matrial}} | {{list.unit}} | 第{{auto_index}}批 |
```

### 清单模板
```
| 项目 | 编号 | 名称 | 数量 | 说明 |
|------|------|------|------|------|
| {{auto_index}} | {{list.code}} | {{list.name}} | {{list.qty}} | 清单第{{auto_index}}项 |
```

### 报表模板
```
| 序号 | 部门 | 员工 | 业绩 | 排名 |
|------|------|------|------|------|
| {{auto_index}} | {{list.dept}} | {{list.name}} | {{list.score}} | 第{{auto_index}}名 |
```

## ✅ 验证方法

### 检查Excel文件
```python
import openpyxl
wb = openpyxl.load_workbook('filled_outputs/filled_xxx.xlsx')
ws = wb.active

# 验证序号列
for row in range(5, 9):  # 数据行
    index_value = ws.cell(row=row, column=1).value
    print(f"第{row}行序号: {index_value}")
```

### 检查HTML显示
- 访问 `http://localhost:5000/display` 查看普通页面
- 访问 `http://localhost:5000/print` 查看打印页面
- 确认序号显示正确

## 🎊 总结

自增序号功能为模板打印系统增加了强大的实用功能：

### 方式1：自动检测
- **✅ 简单易用**：无需修改模板
- **✅ 向后兼容**：现有模板直接可用
- **✅ 自动化**：系统自动处理

### 方式2：{{auto_index}} 占位符
- **✅ 灵活强大**：任意位置，任意格式
- **✅ 文本嵌入**：支持复杂的文本组合
- **✅ 多次使用**：同一行可多次使用
- **✅ 完全控制**：用户完全控制序号行为

**推荐使用方式2（{{auto_index}} 占位符）**，因为它提供了更大的灵活性和控制力，是未来发展的方向！

现在您的模板打印系统支持两种强大的自增序号功能，可以满足各种复杂的业务需求！
