import openpyxl
import os

# 找到最新的填充文件
filled_files = []
if os.path.exists('filled_outputs'):
    filled_files = [f for f in os.listdir('filled_outputs') if f.endswith('.xlsx')]
    filled_files.sort(key=lambda x: os.path.getctime(os.path.join('filled_outputs', x)), reverse=True)

if not filled_files:
    print("❌ 没有找到填充文件")
    exit()

latest_file = os.path.join('filled_outputs', filled_files[0])
print(f"检查文件: {latest_file}")

# 加载Excel文件
wb = openpyxl.load_workbook(latest_file)
ws = wb.active

print("\n=== Excel原始数据 ===")
for row in range(1, 8):
    row_data = []
    for col in range(1, 10):
        cell = ws.cell(row=row, column=col)
        value = cell.value if cell.value else ''
        row_data.append(f"'{value}'")
    print(f'第{row}行: {row_data}')

print("\n=== 合并单元格信息 ===")
merged_ranges = list(ws.merged_cells.ranges)
for i, merged_range in enumerate(merged_ranges):
    print(f"合并区域{i+1}: {merged_range}")

print("\n=== 模拟HTML转换过程 ===")
max_row = ws.max_row
max_col = ws.max_column

# 创建单元格矩阵
cell_matrix = []
for r in range(max_row):
    row = []
    for c in range(max_col):
        row.append({
            'value': '',
            'colspan': 1,
            'rowspan': 1,
            'skip': False
        })
    cell_matrix.append(row)

# 填充数据
for row_idx, row in enumerate(ws.iter_rows()):
    for col_idx, cell in enumerate(row):
        if cell.value is not None:
            cell_matrix[row_idx][col_idx]['value'] = str(cell.value)

# 处理合并单元格
for merged_range in merged_ranges:
    min_row = merged_range.min_row - 1
    max_row_range = merged_range.max_row - 1
    min_col = merged_range.min_col - 1
    max_col_range = merged_range.max_col - 1
    
    print(f"处理合并区域: 行{min_row+1}-{max_row_range+1}, 列{min_col+1}-{max_col_range+1}")
    
    # 设置合并单元格的跨度
    cell_matrix[min_row][min_col]['colspan'] = max_col_range - min_col + 1
    cell_matrix[min_row][min_col]['rowspan'] = max_row_range - min_row + 1
    
    # 标记其他被合并的单元格为跳过
    for r in range(min_row, max_row_range + 1):
        for c in range(min_col, max_col_range + 1):
            if r != min_row or c != min_col:
                cell_matrix[r][c]['skip'] = True

print("\n=== 转换后的单元格矩阵 ===")
for row_idx, row in enumerate(cell_matrix):
    print(f"第{row_idx+1}行:")
    for col_idx, cell in enumerate(row):
        if cell['skip']:
            print(f"  第{col_idx+1}列: [跳过]")
        else:
            colspan_info = f" colspan={cell['colspan']}" if cell['colspan'] > 1 else ""
            rowspan_info = f" rowspan={cell['rowspan']}" if cell['rowspan'] > 1 else ""
            print(f"  第{col_idx+1}列: '{cell['value']}'{colspan_info}{rowspan_info}")

print("\n=== 重点检查第5行 ===")
row_5 = cell_matrix[4]  # 第5行，索引为4
print("第5行单元格状态:")
for col_idx, cell in enumerate(row_5):
    status = "[跳过]" if cell['skip'] else f"'{cell['value']}'"
    print(f"  第{col_idx+1}列: {status}")

print("\n=== 模拟HTML生成 ===")
def generate_test_html_row(row_data, row_num):
    html = f'<tr> <!-- 第{row_num}行 -->'
    col_counter = 1
    for cell_data in row_data:
        if cell_data['skip']:
            continue
        
        colspan_attr = f' colspan="{cell_data["colspan"]}"' if cell_data['colspan'] > 1 else ''
        rowspan_attr = f' rowspan="{cell_data["rowspan"]}"' if cell_data['rowspan'] > 1 else ''
        
        value = cell_data['value'] if cell_data['value'] else '&nbsp;'
        html += f'<td{colspan_attr}{rowspan_attr}>{value}</td> <!-- 逻辑列{col_counter} -->'
        col_counter += 1
    
    html += '</tr>'
    return html

# 生成第5行的HTML
row_5_html = generate_test_html_row(row_5, 5)
print(f"第5行HTML: {row_5_html}")

print("\n=== 分析问题 ===")
# 检查第5行是否有被跳过的单元格
skipped_cols = [i+1 for i, cell in enumerate(row_5) if cell['skip']]
if skipped_cols:
    print(f"⚠️ 第5行有被跳过的列: {skipped_cols}")
    print("这可能导致列对齐问题")
else:
    print("✅ 第5行没有被跳过的列")

# 检查数据是否在正确位置
expected_data = ["", "TEST001", "测试商品1", "塑料", "个"]
actual_data = [cell['value'] for cell in row_5[:5]]

print(f"期望数据: {expected_data}")
print(f"实际数据: {actual_data}")

if expected_data == actual_data:
    print("✅ 数据位置正确")
else:
    print("❌ 数据位置错误")
    for i, (exp, act) in enumerate(zip(expected_data, actual_data)):
        if exp != act:
            print(f"  第{i+1}列: 期望 '{exp}', 实际 '{act}'")
