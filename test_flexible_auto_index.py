import requests

# 测试灵活的 {{auto_index}} 占位符功能
data = {
    "data": {
        "list": [
            {"code": "P001", "name": "高端产品A", "spec": "规格A-1", "unit": "台"},
            {"code": "P002", "name": "标准产品B", "spec": "规格B-2", "unit": "套"},
            {"code": "P003", "name": "经济产品C", "spec": "规格C-3", "unit": "个"}
        ],
        "name": "灵活序号演示客户",
        "creator": "序号功能测试员"
    },
    "template_name": "flexible_auto_index"
}

print("🎯 测试灵活的 {{auto_index}} 占位符功能")
print("=" * 60)

# 发送请求
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    
    print("\n📋 测试内容:")
    print("- 模板: flexible_auto_index.xlsx")
    print("- 第1列: {{auto_index}} - 纯序号")
    print("- 第6列: 第{{auto_index}}项 - 嵌入文本中的序号")
    print("- 数据: 3行测试数据")
    
    print("\n🎨 预期显示效果:")
    print("序号 | 编号 | 商品名称   | 规格     | 单位 | 备注")
    print("-" * 55)
    print(" 1   | P001 | 高端产品A  | 规格A-1  | 台   | 第1项")
    print(" 2   | P002 | 标准产品B  | 规格B-2  | 套   | 第2项")
    print(" 3   | P003 | 经济产品C  | 规格C-3  | 个   | 第3项")
    
    print("\n✨ {{auto_index}} 占位符的灵活性:")
    print("🔢 纯序号: {{auto_index}} → 1, 2, 3...")
    print("📝 嵌入文本: 第{{auto_index}}项 → 第1项, 第2项, 第3项...")
    print("🎯 任意位置: 可以放在任意列")
    print("🔄 多次使用: 同一行可以多次使用相同序号")
    print("🎨 样式继承: 继承模板单元格的所有样式")
    
    print("\n🆚 与自动检测的区别:")
    print("自动检测: 系统自动检测空的第1列")
    print("{{auto_index}}: 用户明确指定位置和格式")
    
    print("\n🔗 查看链接:")
    print("   📄 普通页面: http://localhost:5000/display")
    print("   🖨️ 打印页面: http://localhost:5000/print")
    
    print("\n💡 使用建议:")
    print("1. 需要明确控制序号位置时使用 {{auto_index}}")
    print("2. 需要在文本中嵌入序号时使用 {{auto_index}}")
    print("3. 需要多个序号列时使用 {{auto_index}}")
    print("4. 简单场景可以使用自动检测功能")
    
else:
    print(f"❌ API调用失败: {response.text}")

print("\n" + "=" * 60)
print("🎊 灵活 {{auto_index}} 占位符测试完成！")
