# 动态列表格式使用指南

## 🎯 支持的格式

系统现在支持两种动态列表占位符格式：

### 1. `{{item.field}}` 格式（原有格式）
```
{{list:list}}
{{item.batno}}  {{item.name}}  {{item.matrial}}
```

### 2. `{{list.field}}` 格式（新增支持）
```
{{list:list}}
{{list.batno}}  {{list.name}}  {{list.matrial}}
```

## 📊 数据格式

您的数据格式：
```json
{
  "template_name": "list_format_delivery",
  "data": {
    "list": [
      {
        "batno": "a",
        "name": "sdad",
        "matrial": "wd",
        "unit": "个",
        "quantity": 2,
        "note": null,
        "delivery_f": 1
      }
    ],
    "name": "测试客户132",
    "creator": "Super Admin"
  }
}
```

## 🔧 模板设计

### 基本结构
```
A7: {{list:list}}           # 动态列表标记
A8: 批次号  B8: 名称  C8: 材质    # 表头
A9: {{list.batno}}  B9: {{list.name}}  C9: {{list.matrial}}  # 模板行
```

### 完整示例
```
A1: 发货单 (合并A1:G1)
A3: 客户名称: {{name}}
A7: {{list:list}}
A8: 批次号    B8: 名称    C8: 材质    D8: 单位    E8: 数量
A9: {{list.batno}}  B9: {{list.name}}  C9: {{list.matrial}}  D9: {{list.unit}}  E9: {{list.quantity}}
A11: 发货说明: {{delivery_note}}
```

## ✅ 功能特性

### 支持的字段类型
- **字符串**: `"sdad"` → 直接显示
- **数字**: `2` → 显示为文本
- **null值**: `null` → 显示为空白
- **布尔值**: `true/false` → 显示为文本

### 样式保持
- ✅ 字体样式（字体、大小、颜色）
- ✅ 对齐方式（左、中、右对齐）
- ✅ 边框样式
- ✅ 背景颜色
- ✅ 行高和列宽

### 动态特性
- ✅ 任意数量的列表项
- ✅ 列表后内容自动调整位置
- ✅ 保持Excel原始格式
- ✅ 支持合并单元格

## 🎨 模板创建步骤

### 1. 创建基本信息区域
```
A1: 标题（可合并单元格）
A3: 客户名称: {{name}}
A4: 制单人: {{creator}}
```

### 2. 添加动态列表标记
```
A7: {{list:list}}  # 必须单独占一行
```

### 3. 设计表头
```
A8: 批次号  B8: 名称  C8: 材质  # 设置样式
```

### 4. 创建模板行
```
A9: {{list.batno}}  B9: {{list.name}}  C9: {{list.matrial}}
```

### 5. 添加列表后内容
```
A11: 发货说明: {{delivery_note}}
A13: 签名区域
```

## 🔍 字段映射

| Excel占位符 | JSON字段 | 示例值 |
|------------|----------|--------|
| `{{list.batno}}` | `"batno"` | `"a"` |
| `{{list.name}}` | `"name"` | `"sdad"` |
| `{{list.matrial}}` | `"matrial"` | `"wd"` |
| `{{list.unit}}` | `"unit"` | `"个"` |
| `{{list.quantity}}` | `"quantity"` | `2` |
| `{{list.note}}` | `"note"` | `null` → 空白 |
| `{{list.delivery_f}}` | `"delivery_f"` | `1` |

## 📋 实际效果

使用您的数据，模板会生成：

| 批次号 | 名称 | 材质 | 单位 | 数量 | 备注 | 发货标志 |
|--------|------|------|------|------|------|----------|
| a | sdad | wd | 个 | 2 | (空) | 1 |
| ds | da | (空) | 发a | 2 | (空) | 1 |
| a das | 阿萨 | 阿发 | 阿发 | 3 | (空) | 1 |
| a阿发 | eqa'd | (空) | 阿发 | 4 | (空) | 1 |

## 🚀 使用方法

### 1. API调用
```bash
POST http://localhost:5000/api/print
Content-Type: application/json

{
  "template_name": "list_format_delivery",
  "data": { ... }
}
```

### 2. 查看结果
- 普通显示: `http://localhost:5000/display`
- 专用打印: `http://localhost:5000/print`

### 3. 打印设置
- 勾选"背景图形"选项
- 选择A4纸张
- 使用专用打印页面避免黑色背景

## 💡 最佳实践

### 模板设计
1. **表头样式**: 使用蓝色背景、白色字体
2. **数据行**: 设置合适的对齐方式
3. **列宽**: 根据内容调整列宽
4. **边框**: 使用细边框增强可读性

### 数据处理
1. **null值**: 系统自动处理为空白
2. **长文本**: 考虑列宽和换行
3. **数字格式**: 保持原始格式
4. **特殊字符**: 支持中文和特殊符号

### 打印优化
1. **使用专用打印页面**: `/print` 路径
2. **浏览器设置**: 勾选背景图形
3. **页面设置**: A4纸张，合适边距
4. **样式保持**: 系统自动保持Excel样式

## 🔧 故障排除

### 列表不显示
- 检查 `{{list:list}}` 标记是否正确
- 确认数据中有 `"list"` 字段
- 验证列表不为空

### 字段为空
- 检查 `{{list.字段名}}` 拼写
- 确认JSON数据中有对应字段
- null值会显示为空白（正常）

### 样式丢失
- 确保模板行设置了样式
- 检查Excel文件是否正确保存
- 使用专用打印页面

现在您可以使用 `{{list.field}}` 格式创建模板，完全兼容您的数据结构！🎉
