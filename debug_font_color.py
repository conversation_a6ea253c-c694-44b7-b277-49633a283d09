import openpyxl

# 检查模板中的字体颜色
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查模板中的字体颜色 ===")

# 检查几个关键单元格的字体颜色
test_cells = [
    (1, 1, "标题行第1列"),
    (4, 2, "表头-批号"),
    (4, 3, "表头-名称"),
    (5, 2, "模板行-批号"),
    (5, 3, "模板行-名称"),
    (6, 1, "合计行"),
]

for row, col, desc in test_cells:
    cell = ws.cell(row=row, column=col)
    print(f"\n{desc} ({row},{col}):")
    print(f"  值: '{cell.value}'")
    
    if cell.font:
        print(f"  字体: {cell.font.name}")
        print(f"  大小: {cell.font.size}")
        print(f"  粗体: {cell.font.bold}")
        
        if cell.font.color:
            print(f"  颜色对象: {cell.font.color}")
            if hasattr(cell.font.color, 'rgb'):
                print(f"  RGB值: {cell.font.color.rgb}")
            if hasattr(cell.font.color, 'type'):
                print(f"  颜色类型: {cell.font.color.type}")
            if hasattr(cell.font.color, 'auto'):
                print(f"  自动颜色: {cell.font.color.auto}")
        else:
            print("  颜色: 未设置")
    else:
        print("  字体: 未设置")

print("\n=== 测试字体颜色提取函数 ===")

def test_extract_color(cell):
    """测试字体颜色提取"""
    if not cell.font or not cell.font.color:
        return "未设置字体颜色"
    
    color = cell.font.color
    
    # 检查是否是自动颜色
    if hasattr(color, 'auto') and color.auto:
        return "自动颜色(通常是黑色)"
    
    # 检查RGB值
    if hasattr(color, 'rgb') and color.rgb:
        rgb = color.rgb
        if isinstance(rgb, str):
            if len(rgb) == 8:
                # 移除alpha通道
                rgb = rgb[2:]
            if len(rgb) == 6:
                return f"#{rgb}"
        return f"RGB格式错误: {rgb}"
    
    # 检查主题颜色
    if hasattr(color, 'theme') and color.theme is not None:
        return f"主题颜色: {color.theme}"
    
    # 检查索引颜色
    if hasattr(color, 'indexed') and color.indexed is not None:
        return f"索引颜色: {color.indexed}"
    
    return "未知颜色格式"

for row, col, desc in test_cells:
    cell = ws.cell(row=row, column=col)
    color_result = test_extract_color(cell)
    print(f"{desc}: {color_result}")

print("\n=== 建议的修复方案 ===")
print("如果字体颜色显示为灰色，可能的原因:")
print("1. Excel中使用了自动颜色，需要强制设置为黑色")
print("2. Excel中使用了主题颜色，需要转换为具体的RGB值")
print("3. 没有设置字体颜色，需要设置默认黑色")
print("4. CSS样式被其他样式覆盖")

print("\n建议修复:")
print("- 在样式提取函数中，如果没有检测到颜色，强制设置为黑色 #000000")
print("- 处理自动颜色和主题颜色的情况")
