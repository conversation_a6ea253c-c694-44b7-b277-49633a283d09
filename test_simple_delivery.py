import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 简化的测试数据，只包含模板中存在的字段
print("=== 测试简化的发货单数据 ===")

data = {
    "template_name": "delivery",
    "data": {
        "list": [
            {
                "batno": "A001",
                "name": "商品1",
                "matrial": "塑料",
                "unit": "个"
            },
            {
                "batno": "B002", 
                "name": "商品2",
                "matrial": "金属",
                "unit": "套"
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    }
}

print("发送的数据:")
print(json.dumps(data, indent=2, ensure_ascii=False))

response = requests.post(url, json=data)
print(f"\n状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ API调用成功！")
    print("请访问以下链接查看结果:")
    print("1. 普通显示: http://localhost:5000/display")
    print("2. 专用打印: http://localhost:5000/print")
    
    print("\n🔍 检查要点:")
    print("- 第5行应该显示: A001 | 商品1 | 塑料 | 个")
    print("- 第6行应该显示: B002 | 商品2 | 金属 | 套")
    print("- {{list.batno}}, {{list.name}}, {{list.matrial}}, {{list.unit}} 应该被正确替换")
    
else:
    print(f"\n❌ API调用失败: {response.text}")

print("\n" + "="*50)
print("模板字段映射:")
print("{{list.batno}} -> batno")
print("{{list.name}} -> name") 
print("{{list.matrial}} -> matrial")
print("{{list.unit}} -> unit")
