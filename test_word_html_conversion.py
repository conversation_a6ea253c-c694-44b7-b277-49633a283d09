import sys
import os
sys.path.append('.')

from docx import Document
from app import convert_word_to_html, process_word_template

print("=== 测试Word模板HTML转换 ===")

# 测试数据
test_data = {
    "cdkh": "HTML_TEST_001",
    "list": [
        {"name": "HTML测试产品A", "quantity": 10, "note": "HTML测试备注A"},
        {"name": "HTML测试产品B", "quantity": 20, "note": "HTML测试备注B"}
    ],
    "creator": "HTML测试员",
    "date": "2025-06-20"
}

template_path = "templates/委外加工过程不良品记录表.docx"

try:
    # 1. 先查看原始Word文档结构
    print("1. 原始Word文档结构:")
    doc = Document(template_path)
    
    print(f"   段落数量: {len(doc.paragraphs)}")
    print(f"   表格数量: {len(doc.tables)}")
    
    print("\n   文档内容顺序:")
    content_order = []
    
    # 获取所有段落和表格的位置
    for i, para in enumerate(doc.paragraphs):
        if para.text.strip():
            content_order.append(f"段落{i+1}: {para.text[:30]}...")
    
    for i, table in enumerate(doc.tables):
        content_order.append(f"表格{i+1}: {len(table.rows)}行x{len(table.columns)}列")
        # 显示表格第一行
        if table.rows:
            first_row_text = []
            for cell in table.rows[0].cells:
                cell_text = cell.text.strip()[:15] if cell.text.strip() else "(空)"
                first_row_text.append(cell_text)
            content_order.append(f"  第1行: {' | '.join(first_row_text)}")
    
    for item in content_order:
        print(f"   {item}")
    
    # 2. 处理Word模板
    print("\n2. 处理Word模板:")
    result = process_word_template(template_path, test_data)
    
    if result.get("success"):
        print("   ✅ Word模板处理成功")
        
        # 3. 重新加载处理后的文档并检查HTML转换
        print("\n3. 检查HTML转换:")
        
        # 直接测试HTML转换函数
        processed_doc = Document(template_path)
        
        # 先处理数据（简化版）
        for para in processed_doc.paragraphs:
            if para.text:
                original_text = para.text
                new_text = original_text
                for key, value in test_data.items():
                    if key != 'list':
                        placeholder = f"{{{{{key}}}}}"
                        if placeholder in new_text:
                            new_text = new_text.replace(placeholder, str(value))
                if new_text != original_text:
                    para.text = new_text
        
        # 转换为HTML
        html_content = convert_word_to_html(processed_doc)
        
        print("   HTML内容预览:")
        print("   " + "="*50)
        
        # 分析HTML结构
        lines = html_content.split('\n')
        for i, line in enumerate(lines[:20]):  # 只显示前20行
            if line.strip():
                print(f"   {i+1:2d}: {line.strip()[:80]}...")
        
        if len(lines) > 20:
            print(f"   ... (还有{len(lines)-20}行)")
        
        print("   " + "="*50)
        
        # 4. 检查签字部分是否在正确位置
        print("\n4. 检查签字部分位置:")
        
        signature_found = False
        signature_position = -1
        
        for i, line in enumerate(lines):
            if "签字" in line or "接收" in line:
                signature_found = True
                signature_position = i
                print(f"   找到签字相关内容在第{i+1}行: {line.strip()}")
        
        if signature_found:
            print(f"   签字部分位置: 第{signature_position+1}行 / 总共{len(lines)}行")
            
            # 检查签字部分是否在表格后面
            table_end_position = -1
            for i, line in enumerate(lines):
                if "</table>" in line:
                    table_end_position = i
            
            if table_end_position > 0 and signature_position > table_end_position:
                print("   ✅ 签字部分正确位于表格后面")
            else:
                print("   ❌ 签字部分位置不正确")
                print(f"   表格结束位置: 第{table_end_position+1}行")
                print(f"   签字部分位置: 第{signature_position+1}行")
        else:
            print("   ❌ 未找到签字相关内容")
        
        # 5. 保存HTML用于检查
        html_file = "debug_word_output.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Word模板HTML转换测试</title>
    <style>
        body {{ font-family: SimSun, serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        td {{ border: 1px solid #000; padding: 4px 8px; }}
    </style>
</head>
<body>
    <h1>Word模板HTML转换测试</h1>
    {html_content}
</body>
</html>
            """)
        
        print(f"\n5. HTML文件已保存: {html_file}")
        print(f"   可以在浏览器中打开查看效果")
        
    else:
        print(f"   ❌ Word模板处理失败: {result}")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Word HTML转换测试完成 ===")
