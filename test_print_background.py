import requests

# 测试简单数据
print("=== 测试打印背景修复 ===")

url = "http://localhost:5000/api/print"
data = {
    "template_name": "delivery",
    "data": {
        "list": [
            {
                "batno": "TEST001",
                "name": "测试商品1",
                "matrial": "塑料",
                "unit": "个",
                "quantity": 5,
                "note": "测试备注",
                "delivery_f": 1
            },
            {
                "batno": "TEST002", 
                "name": "测试商品2",
                "matrial": "金属",
                "unit": "套",
                "quantity": 3,
                "note": None,
                "delivery_f": 1
            }
        ],
        "name": "测试客户",
        "creator": "测试员",
        "delivery_date": "2023-12-01",
        "delivery_note": "这是一个测试发货单，用于验证打印背景是否为白色。"
    }
}

response = requests.post(url, json=data)
print(f"API状态码: {response.status_code}")
print(f"API响应: {response.text}")

if response.status_code == 200:
    print("\n🎯 测试页面链接:")
    print("1. 普通显示页面: http://localhost:5000/display")
    print("2. 专用打印页面: http://localhost:5000/print")
    
    print("\n📋 测试步骤:")
    print("1. 访问普通显示页面，点击'专用打印页面'按钮")
    print("2. 在专用打印页面按 Ctrl+P 查看打印预览")
    print("3. 检查打印预览背景是否为白色")
    
    print("\n🔍 如果仍然是黑色背景，请检查:")
    print("- 浏览器是否开启了深色模式")
    print("- 系统是否使用深色主题")
    print("- 是否勾选了'背景图形'选项")
    print("- 尝试使用无痕模式")
    
    print("\n💡 推荐解决方案:")
    print("1. 使用专用打印页面 (http://localhost:5000/print)")
    print("2. 在Chrome中: 设置 → 外观 → 主题 → 浅色")
    print("3. 打印时勾选'背景图形'选项")
    print("4. 清除浏览器缓存后重试")
else:
    print(f"❌ API测试失败: {response.text}")
