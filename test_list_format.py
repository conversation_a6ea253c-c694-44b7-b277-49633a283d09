import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 测试 {{list.field}} 格式的模板
print("=== 测试 {{list.field}} 格式模板 ===")

# 使用您提供的真实数据格式
data = {
    "template_name": "list_format_delivery",
    "data": {
        "list": [
            {
                "createdAt": "2025-05-28T07:32:46.190Z",
                "updatedAt": "2025-05-28T09:44:59.152Z",
                "id": 1,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a",
                "name": "sdad",
                "matrial": "wd",
                "unit": "个",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.156Z",
                "updatedAt": "2025-05-28T09:44:59.156Z",
                "id": 2,
                "createdById": 1,
                "updatedById": 1,
                "batno": "ds",
                "name": "da",
                "matrial": None,
                "unit": "发a",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.158Z",
                "updatedAt": "2025-05-28T09:44:59.158Z",
                "id": 3,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a das",
                "name": "阿萨 ",
                "matrial": "阿发",
                "unit": "阿发",
                "quantity": 3,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.160Z",
                "updatedAt": "2025-05-28T09:44:59.160Z",
                "id": 4,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a阿发",
                "name": "eqa'd",
                "matrial": None,
                "unit": "阿发",
                "quantity": 4,
                "note": None,
                "amount": None,
                "delivery_f": 1
            }
        ],
        "name": "测试客户132",
        "creator": "Super Admin",
        "delivery_date": "2023-12-01",
        "delivery_note": "这是使用 {{list.field}} 格式的测试发货单。"
    }
}

response = requests.post(url, json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ 测试成功！")
    print("请访问以下链接查看结果:")
    print("1. 普通显示: http://localhost:5000/display")
    print("2. 专用打印: http://localhost:5000/print")
    
    print("\n🔍 检查要点:")
    print("- {{list.batno}} 是否正确显示为批次号")
    print("- {{list.name}} 是否正确显示为名称")
    print("- {{list.matrial}} 是否正确显示为材质")
    print("- {{list.unit}} 是否正确显示为单位")
    print("- {{list.quantity}} 是否正确显示为数量")
    print("- {{list.note}} 的None值是否显示为空")
    print("- {{list.delivery_f}} 是否正确显示")
    
    print("\n📋 预期结果:")
    print("第1行: a | sdad | wd | 个 | 2 | (空) | 1")
    print("第2行: ds | da | (空) | 发a | 2 | (空) | 1")
    print("第3行: a das | 阿萨 | 阿发 | 阿发 | 3 | (空) | 1")
    print("第4行: a阿发 | eqa'd | (空) | 阿发 | 4 | (空) | 1")
else:
    print(f"\n❌ 测试失败: {response.text}")

print("\n" + "="*60)
print("格式对比:")
print("✅ {{list.field}} - 您使用的格式，现在已支持")
print("✅ {{item.field}} - 原有格式，继续支持")
print("✅ 两种格式可以混用")
print("✅ 完全兼容您的数据结构")
