from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side

# 创建工作簿和工作表
wb = Workbook()
ws = wb.active
ws.title = "打印模板"

# 设置单元格样式
header_font = Font(name='宋体', size=12, bold=True)
normal_font = Font(name='宋体', size=11)
border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 添加表头信息
ws['A1'] = "打印日期: {{date}}"
ws['A2'] = "单位名称: {{company}}"
ws['A3'] = "联系人: {{contact}}"
ws['A4'] = "电话: {{phone}}"

# 设置表头样式
for cell in ['A1', 'A2', 'A3', 'A4']:
    ws[cell].font = normal_font

# 添加表格标题
ws['B6'] = "序号"
ws['C6'] = "商品名称"
ws['D6'] = "数量"
ws['E6'] = "单价"
ws['F6'] = "金额"

# 设置表格标题样式
for col in ['B6', 'C6', 'D6', 'E6', 'F6']:
    ws[col].font = header_font
    ws[col].alignment = Alignment(horizontal='center')
    ws[col].border = border

# 添加表格内容
ws['B7'] = 1
ws['C7'] = "{{item1_name}}"
ws['D7'] = "{{item1_quantity}}"
ws['E7'] = "{{item1_price}}"
ws['F7'] = "={{item1_quantity}}*{{item1_price}}"

ws['B8'] = 2
ws['C8'] = "{{item2_name}}"
ws['D8'] = "{{item2_quantity}}"
ws['E8'] = "{{item2_price}}"
ws['F8'] = "={{item2_quantity}}*{{item2_price}}"

# 设置表格内容样式
for row in range(7, 9):
    for col in ['B', 'C', 'D', 'E', 'F']:
        cell = f"{col}{row}"
        ws[cell].font = normal_font
        ws[cell].border = border

# 添加合计和备注
ws['A10'] = "合计金额: {{total_amount}}"
ws['A11'] = "备注: {{remarks}}"

# 设置合计和备注样式
ws['A10'].font = normal_font
ws['A11'].font = normal_font

# 调整列宽
ws.column_dimensions['A'].width = 15
ws.column_dimensions['B'].width = 8
ws.column_dimensions['C'].width = 20
ws.column_dimensions['D'].width = 10
ws.column_dimensions['E'].width = 10
ws.column_dimensions['F'].width = 12

# 保存工作簿
wb.save("templates/dayinceshi.xlsx")
print("Excel模板已创建: templates/dayinceshi.xlsx")
