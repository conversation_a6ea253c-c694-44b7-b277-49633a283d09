import sys
import os
sys.path.append('.')

from docx import Document
from app import convert_word_to_html

print("=== 测试修复后的HTML转换 ===")

try:
    # 加载Word文档
    template_path = "templates/委外加工过程不良品记录表.docx"
    doc = Document(template_path)
    
    print("1. Word文档结构:")
    print(f"   段落数量: {len(doc.paragraphs)}")
    print(f"   表格数量: {len(doc.tables)}")
    
    # 显示段落内容
    print("\n2. 段落内容:")
    for i, para in enumerate(doc.paragraphs):
        if para.text.strip():
            print(f"   段落{i+1}: {para.text[:40]}...")
    
    # 转换为HTML
    print("\n3. 转换为HTML:")
    html_content = convert_word_to_html(doc)
    
    # 分析HTML结构
    print("   HTML结构分析:")
    
    # 查找段落和表格的位置
    import re
    
    # 查找所有<p>标签
    p_matches = list(re.finditer(r'<p[^>]*>(.*?)</p>', html_content))
    print(f"   找到 {len(p_matches)} 个段落标签")
    
    # 查找所有<table>标签
    table_matches = list(re.finditer(r'<table[^>]*>', html_content))
    print(f"   找到 {len(table_matches)} 个表格标签")
    
    # 检查签字部分的位置
    signature_positions = []
    for i, match in enumerate(p_matches):
        content = match.group(1)
        if "签字" in content or "接收" in content:
            signature_positions.append((match.start(), content))
            print(f"   签字段落{i+1} 位置: {match.start()}, 内容: {content}")
    
    # 检查表格的位置
    table_positions = []
    for i, match in enumerate(table_matches):
        table_positions.append(match.start())
        print(f"   表格{i+1} 位置: {match.start()}")
    
    # 验证签字部分是否在表格后面
    print("\n4. 位置验证:")
    if signature_positions and table_positions:
        last_table_pos = max(table_positions)
        first_signature_pos = min(pos for pos, _ in signature_positions)
        
        if first_signature_pos > last_table_pos:
            print("   ✅ 签字部分正确位于表格后面")
        else:
            print("   ❌ 签字部分仍然在表格前面")
            print(f"   最后表格位置: {last_table_pos}")
            print(f"   第一个签字位置: {first_signature_pos}")
    
    # 保存HTML文件用于查看
    html_file = "fixed_word_output.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>修复后的Word模板HTML</title>
    <style>
        body {{ font-family: SimSun, serif; margin: 20px; background-color: white; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        td {{ border: 1px solid #000; padding: 4px 8px; }}
        p {{ margin: 8px 0; line-height: 1.5; }}
        .signature-section {{ 
            margin-top: 20px; 
            padding: 10px; 
            background-color: #f9f9f9; 
            border: 1px dashed #ccc; 
        }}
    </style>
</head>
<body>
    <h1>修复后的Word模板HTML转换</h1>
    {html_content}
</body>
</html>
        """)
    
    print(f"\n5. HTML文件已保存: {html_file}")
    print("   可以在浏览器中打开查看效果")
    
    # 显示HTML内容的前500个字符
    print(f"\n6. HTML内容预览:")
    print("   " + "="*60)
    print(f"   {html_content[:500]}...")
    print("   " + "="*60)

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 修复测试完成 ===")
