import openpyxl
import requests
import json
from io import BytesIO

# 1. 首先检查原始模板
print("=== 1. 检查原始模板 ===")
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active
print("原始模板第5行内容:")
for col in range(1, 8):
    cell = ws.cell(row=5, column=col)
    print(f"  第{col}列: {cell.value}")

# 2. 发送API请求
print("\n=== 2. 发送API请求 ===")
data = {
    "data": {
        "list": [
            {
                "batno": "TEST001",
                "name": "测试商品1",
                "matrial": "塑料",
                "unit": "个",
                "quantity": 2
            },
            {
                "batno": "TEST002",
                "name": "测试商品2",
                "matrial": "金属",
                "unit": "套",
                "quantity": 3
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API响应状态: {response.status_code}")
print(f"API响应内容: {response.text}")

# 3. 检查处理后的结果
print("\n=== 3. 检查显示页面内容 ===")
display_response = requests.get("http://localhost:5000/display")
print(f"显示页面状态: {display_response.status_code}")

# 检查HTML内容中是否包含我们的数据
html_content = display_response.text
if "TEST001" in html_content:
    print("✅ 找到TEST001数据")
else:
    print("❌ 未找到TEST001数据")

if "测试商品1" in html_content:
    print("✅ 找到测试商品1数据")
else:
    print("❌ 未找到测试商品1数据")

if "{{list.batno}}" in html_content:
    print("❌ 仍然包含未替换的占位符 {{list.batno}}")
else:
    print("✅ 占位符已被替换")

# 4. 检查HTML表格结构
print("\n=== 4. 分析HTML表格结构 ===")
if "<table" in html_content:
    print("✅ 找到表格标签")
    # 计算表格行数
    tr_count = html_content.count("<tr")
    print(f"表格行数: {tr_count}")
    
    # 查找包含数据的行
    lines = html_content.split('\n')
    for i, line in enumerate(lines):
        if "TEST001" in line or "测试商品1" in line:
            print(f"第{i+1}行包含数据: {line.strip()}")
else:
    print("❌ 未找到表格标签")

print("\n=== 调试完成 ===")
print("如果数据没有正确显示，问题可能在于:")
print("1. 动态列表处理逻辑")
print("2. Excel文件保存和读取")
print("3. HTML转换过程")
print("4. 占位符替换逻辑")
