<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 打印版</title>
    <style>
        /* 重置所有样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            background: white !important;
            background-color: white !important;
            color: black !important;
            color-scheme: light !important;
            forced-color-adjust: none !important;
        }

        html {
            background: white !important;
            background-color: white !important;
            color: black !important;
            color-scheme: light !important;
            forced-color-adjust: none !important;
        }

        body {
            background: white !important;
            background-color: white !important;
            color: black !important;
            font-family: 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color-scheme: light !important;
            forced-color-adjust: none !important;
        }

        /* 页面设置 */
        @page {
            margin: 1cm;
            background: white !important;
            background-color: white !important;
            color: black !important;
            size: A4 portrait;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            background: white !important;
            background-color: white !important;
            color: black !important;
            margin: 0;
            page-break-inside: auto;
        }

        th, td {
            border: 1px solid black !important;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
            background: white !important;
            background-color: white !important;
            color: black !important;
            page-break-inside: avoid;
        }

        th {
            font-weight: bold;
            text-align: center;
            background: #f0f0f0 !important;
            background-color: #f0f0f0 !important;
        }

        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
            color: black !important;
            background: white !important;
            background-color: white !important;
            margin: 10px 0;
        }

        /* 段落样式 */
        p, div, span {
            color: black !important;
            background: white !important;
            background-color: white !important;
        }

        /* 强制覆盖任何可能的深色样式，但保留内联颜色 */
        *, *::before, *::after {
            background: white !important;
            background-color: white !important;
            text-shadow: none !important;
            box-shadow: none !important;
            background-image: none !important;
            color-scheme: light !important;
            forced-color-adjust: none !important;
        }

        /* 只对没有内联颜色样式的元素设置黑色 */
        body, div:not([style*="color"]), p:not([style*="color"]), span:not([style*="color"]) {
            color: black !important;
        }

        /* 强制确保红色字体显示 */
        [style*="color: #FF0000"], [style*="color: #ff0000"] {
            color: #FF0000 !important;
        }

        /* 针对深色模式的特殊处理 */
        @media (prefers-color-scheme: dark) {
            html, body {
                background: white !important;
                background-color: white !important;
                color: black !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            table, th, td, tr {
                background: white !important;
                background-color: white !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }
        }

        /* 打印时的额外保护 */
        @media print {
            *, *::before, *::after {
                background: white !important;
                background-color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-scheme: light !important;
                forced-color-adjust: none !important;
            }

            html, body {
                background: white !important;
                background-color: white !important;
                color: black !important;
            }

            @page {
                background: white !important;
                background-color: white !important;
            }
        }

        /* 隐藏打印按钮 */
        .print-controls {
            display: none;
        }

        @media screen {
            .print-controls {
                display: block;
                text-align: center;
                margin: 20px 0;
                background: #f0f0f0 !important;
                padding: 10px;
                border: 1px solid #ccc;
            }

            .print-btn {
                background: #4CAF50 !important;
                color: white !important;
                border: none;
                padding: 10px 20px;
                font-size: 16px;
                border-radius: 4px;
                cursor: pointer;
                margin: 0 5px;
            }

            .print-btn:hover {
                background: #45a049 !important;
            }
        }
    </style>
    <script>
        function printPage() {
            window.print();
        }

        function goBack() {
            window.history.back();
        }

        // 自动打印（可选）
        // window.onload = function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // };
    </script>
</head>
<body>
    <div class="print-controls">
        <h2>{{ title }}</h2>
        <p>这是专门的打印页面，背景强制为白色</p>
        <button class="print-btn" onclick="printPage()">🖨️ 打印</button>
        <button class="print-btn" onclick="goBack()">🔙 返回</button>
        <p><small>生成时间: {{ timestamp }}</small></p>
    </div>

    <div class="content">
        {{ content|safe }}
    </div>
</body>
</html>
