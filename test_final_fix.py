import requests

# 测试最终修复
data = {
    "data": {
        "list": [
            {
                "batno": "TEST001",
                "name": "测试商品1",
                "matrial": "塑料",
                "unit": "个"
            },
            {
                "batno": "TEST002",
                "name": "测试商品2",
                "matrial": "金属",
                "unit": "套"
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 最终修复测试 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ 请检查结果:")
    print("访问: http://localhost:5000/display")
    print("期望看到:")
    print("- 第5行: TEST001 | 测试商品1 | 塑料 | 个")
    print("- 第6行: TEST002 | 测试商品2 | 金属 | 套")
    print("- 不应该看到 {{list.batno}} 等占位符")
else:
    print(f"❌ 失败: {response.text}")
