import requests

# 修复后的Word模板测试 - 使用正确的字段名
data = {
    "data": {
        "cdkh": "WORD20250620001",  # 修正：使用cdkh而不是ccdkh
        "list": [
            {"batno": "WORD001", "name": "Word测试产品A", "matrial": "测试材料A", "unit": "个", "quantity": 15, "price": 200, "amount": 3000, "note": "Word测试A"},
            {"batno": "WORD002", "name": "Word测试产品B", "matrial": "测试材料B", "unit": "套", "quantity": 25, "price": 300, "amount": 7500, "note": "Word测试B"},
            {"batno": "WORD003", "name": "Word测试产品C", "matrial": "测试材料C", "unit": "件", "quantity": 10, "price": 500, "amount": 5000, "note": "Word测试C"}
        ],
        "name": "Word模板测试客户",
        "creator": "Word测试员",
        "date": "2025-06-20",
        "deliveryman": "Word送货员"
    },
    "template_name": "委外加工过程不良品记录表"
}

print("📄 修复后的Word模板测试")
print("=" * 50)

print("🔧 修复内容:")
print("- 字段名修正: ccdkh -> cdkh")
print("- 确保与Word模板中的占位符一致")
print("- 添加完整的测试数据")

try:
    # 发送请求
    response = requests.post("http://localhost:5000/api/print", json=data, timeout=30)
    print(f"\nAPI状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API调用成功")
        result = response.json()
        print(f"响应: {result}")
        
        print("\n📋 Word模板特点:")
        print("- 表格1: 基本信息表格 (4行x4列)")
        print("- 表格2: 不良品明细记录 (6行x6列)")
        print("- 占位符: {{cdkh}} 等")
        
        print("\n🎯 预期功能:")
        print("✅ 基本占位符: {{cdkh}} -> WORD20250620001")
        print("✅ 动态列表: 在表格2中生成3行数据")
        print("✅ 自增序号: 1, 2, 3")
        print("✅ SUM计算: 如果有sum占位符")
        
        print("\n🔗 查看链接:")
        print("   📄 普通页面: http://localhost:5000/display")
        print("   🖨️ 打印页面: http://localhost:5000/print")
        
        print("\n💡 Word模板结构:")
        print("表格1 - 基本信息:")
        print("  加工商名称 | □文安 □义通 | 生产传递卡号 | {{cdkh}}")
        print("  产品名称   | 减震弹簧     | 委外加工内容 | ")
        print("  发出日期   |             | 发出数量     | ")
        print("  记录表填写日期 |         | 加工商负责人 | ")
        
        print("\n表格2 - 不良品明细:")
        print("  序号 | 不良品发现工序 | 不良品描述 | 数量 | 判定责任方 | 备注")
        print("  1    | {{list.name}}  | {{list.matrial}} | {{list.quantity}} | 来料不良/本工序造成 | {{list.note}}")
        print("  2    | ...            | ...              | ...               | ...                | ...")
        
    else:
        print(f"❌ API调用失败: {response.text}")
        
        if response.status_code == 503:
            print("\n🔍 503错误可能原因:")
            print("- 服务器内部错误")
            print("- Word处理函数异常")
            print("- 缺少依赖包")
            print("- 模板文件问题")
        
except requests.exceptions.Timeout:
    print("⏰ 请求超时，Word处理可能需要更长时间")
except Exception as e:
    print(f"❌ 请求异常: {e}")

print("\n" + "=" * 50)
print("📄 修复后的Word测试完成")

# 额外的调试信息
print("\n🔍 调试信息:")
print("Word模板路径: templates/委外加工过程不良品记录表.docx")
print("测试数据字段: cdkh, list, name, creator, date, deliveryman")
print("预期占位符: {{cdkh}}, {{list.field}}, {{auto_index}}, {{sum.field}}")
