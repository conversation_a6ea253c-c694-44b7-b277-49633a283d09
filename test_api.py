import requests
import json

# 测试首页
response = requests.get("http://localhost:5000")
print(f"首页状态码: {response.status_code}")
print(f"首页内容长度: {len(response.text)}")

# API端点
url = "http://localhost:5000/api/print"

# 测试数据 - 只使用英文和数字
data = {
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-01",
        "company": "Test Company",
        "contact": "John Doe",
        "phone": "13800138000",
        "item1_name": "Product 1",
        "item1_quantity": "10",
        "item1_price": "100",
        "item2_name": "Product 2",
        "item2_quantity": "5",
        "item2_price": "200",
        "total_amount": "2000",
        "remarks": "Test remarks"
    }
}

# 发送POST请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, json=data, headers=headers)

# 打印响应
print(f"API状态码: {response.status_code}")
print(f"API响应内容: {response.text}")

# 如果成功，获取显示页面
if response.status_code == 200:
    display_response = requests.get("http://localhost:5000/display")
    print(f"显示页面状态码: {display_response.status_code}")
    print(f"显示页面内容长度: {len(display_response.text)}")
    print("\n请访问 http://localhost:5000/display 查看打印结果")
