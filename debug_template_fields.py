import openpyxl

# 检查模板中的实际字段名
wb = openpyxl.load_workbook('templates/delivery_with_sum_placeholders.xlsx')
ws = wb.active

print("=== 检查模板中的字段名 ===")

print("第5行（模板行）内容:")
for col in range(1, 10):
    cell = ws.cell(row=5, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

print("\n第6行（合计行）内容:")
for col in range(1, 10):
    cell = ws.cell(row=6, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

print("\n=== 分析字段映射 ===")
print("从模板可以看出:")
print("第6列应该是数量字段，但模板中可能不是 {{list.qty}}")
print("第8列应该是金额字段，但模板中可能不是 {{list.amount}}")

print("\n=== 检查原始模板 ===")
wb_original = openpyxl.load_workbook('templates/delivery.xlsx')
ws_original = wb_original.active

print("原始模板第5行:")
for col in range(1, 10):
    cell = ws_original.cell(row=5, column=col)
    value = cell.value if cell.value else "(空)"
    print(f"  第{col}列: '{value}'")

print("\n=== 问题分析 ===")
print("1. 需要检查模板中的实际字段名")
print("2. 数据中的字段名需要与模板匹配")
print("3. SUM占位符可能没有被正确处理")

print("\n=== 建议的修复方案 ===")
print("1. 修正模板中的字段名")
print("2. 确保数据字段名与模板一致")
print("3. 检查SUM占位符处理逻辑")
