<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板打印系统 - 使用说明书</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .toc {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 10px;
        }
        .toc li {
            margin-bottom: 8px;
        }
        .toc a {
            text-decoration: none;
            color: #4CAF50;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .section:last-child {
            border-bottom: none;
        }
        .code-block {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>模板打印系统</h1>
        </header>
        <main>
            <h1>使用说明书</h1>

            <div class="toc">
                <h3>目录</h3>
                <ul>
                    <li><a href="#overview">1. 系统概述</a></li>
                    <li><a href="#requirements">2. 系统要求</a></li>
                    <li><a href="#templates">3. 模板准备</a></li>
                    <li><a href="#list-functions">4. 列表功能详解</a></li>
                    <li><a href="#api">5. API使用说明</a></li>
                    <li><a href="#view-print">6. 查看和打印</a></li>
                    <li><a href="#nocobase">7. 与nocobase集成</a></li>
                    <li><a href="#faq">8. 常见问题</a></li>
                </ul>
            </div>

            <section id="overview" class="section">
                <h2>1. 系统概述</h2>
                <p>模板打印系统是一个基于Python开发的Web应用，用于接收打印请求，填充Excel或Word模板，并将结果渲染到HTML页面上供用户查看和打印。本系统主要面向需要从nocobase或其他系统接收数据并生成打印页面的场景。</p>
                <p>系统特点：</p>
                <ul>
                    <li>支持Excel和Word模板</li>
                    <li>通过简单的API接口接收数据</li>
                    <li>自动填充模板并生成打印页面</li>
                    <li>纯白背景打印，无阴影框</li>
                    <li>易于与其他系统集成</li>
                </ul>
            </section>

            <section id="requirements" class="section">
                <h2>2. 系统要求</h2>
                <p>本系统基于以下技术构建：</p>
                <ul>
                    <li>Python 3.6+</li>
                    <li>Flask (Web框架)</li>
                    <li>openpyxl (Excel处理)</li>
                    <li>python-docx (Word处理)</li>
                </ul>
            </section>

            <section id="templates" class="section">
                <h2>3. 模板准备</h2>

                <h3>3.1 Excel模板</h3>
                <p>创建Excel模板的步骤：</p>
                <ol>
                    <li>创建Excel文件（.xlsx格式）</li>
                    <li>在需要填充数据的单元格中使用 <code>{{变量名}}</code> 格式的占位符</li>
                    <li>将模板文件保存到系统的 <code>templates</code> 目录下</li>
                </ol>
                <p>示例：</p>
                <ul>
                    <li>单元格A1: <code>打印日期: {{date}}</code></li>
                    <li>单元格B2: <code>客户名称: {{customer}}</code></li>
                </ul>

                <h3>3.2 Word模板</h3>
                <p>创建Word模板的步骤：</p>
                <ol>
                    <li>创建Word文件（.docx格式）</li>
                    <li>在需要填充数据的位置使用 <code>{{变量名}}</code> 格式的占位符</li>
                    <li>将模板文件保存到系统的 <code>templates</code> 目录下</li>
                </ol>
                <p>示例：</p>
                <ul>
                    <li><code>申请日期: {{date}}</code></li>
                    <li><code>申请人: {{applicant}}</code></li>
                </ul>
            </section>

            <section id="list-functions" class="section">
                <h2>4. 列表功能详解</h2>
                <p>系统支持多种方式处理列表数据，以满足不同的业务需求。</p>

                <h3>4.1 方法1：固定数量的列表项（推荐）</h3>
                <p>这是最稳定和可控的方法，适合大多数场景。</p>

                <p><strong>Excel模板示例：</strong></p>
                <div class="code-block">
A8: 序号    B8: 商品名称         C8: 数量              D8: 单价              E8: 小计
A9: 1       B9: {{items1_name}}  C9: {{items1_quantity}} D9: {{items1_price}}  E9: {{items1_total}}
A10: 2      B10: {{items2_name}} C10: {{items2_quantity}} D10: {{items2_price}} E10: {{items2_total}}
A11: 3      B11: {{items3_name}} C11: {{items3_quantity}} D11: {{items3_price}} E11: {{items3_total}}
                </div>

                <p><strong>对应的API请求：</strong></p>
                <div class="code-block">
{
    "template_name": "商品清单",
    "data": {
        "items1_name": "苹果",
        "items1_quantity": "10",
        "items1_price": "5.00",
        "items1_total": "50.00",
        "items2_name": "香蕉",
        "items2_quantity": "20",
        "items2_price": "3.00",
        "items2_total": "60.00",
        "items3_name": "橙子",
        "items3_quantity": "15",
        "items3_price": "4.00",
        "items3_total": "60.00"
    }
}
                </div>

                <h3>4.2 方法2：使用列表数据结构（自动转换）</h3>
                <p>当您有结构化的列表数据时，系统会自动将其转换为索引格式。</p>

                <p><strong>API请求示例：</strong></p>
                <div class="code-block">
{
    "template_name": "商品清单",
    "data": {
        "items": [
            {"name": "笔记本电脑", "quantity": "1", "price": "5000.00", "total": "5000.00"},
            {"name": "鼠标", "quantity": "2", "price": "50.00", "total": "100.00"},
            {"name": "键盘", "quantity": "1", "price": "200.00", "total": "200.00"}
        ]
    }
}
                </div>

                <p><strong>系统会自动转换为：</strong></p>
                <ul>
                    <li>items1_name, items1_quantity, items1_price, items1_total</li>
                    <li>items2_name, items2_quantity, items2_price, items2_total</li>
                    <li>items3_name, items3_quantity, items3_price, items3_total</li>
                </ul>

                <h3>4.3 方法3：简单列表</h3>
                <p>适用于简单的列表数据。</p>

                <p><strong>API请求示例：</strong></p>
                <div class="code-block">
{
    "data": {
        "products": ["产品A", "产品B", "产品C"]
    }
}
                </div>

                <p><strong>系统会自动转换为：</strong></p>
                <ul>
                    <li>products1: "产品A"</li>
                    <li>products2: "产品B"</li>
                    <li>products3: "产品C"</li>
                </ul>

                <h3>4.4 Word模板中的列表</h3>
                <p>在Word模板中，您可以使用相同的占位符格式：</p>
                <div class="code-block">
商品清单：
1. 商品名称：{{items1_name}}，数量：{{items1_quantity}}，单价：{{items1_price}}
2. 商品名称：{{items2_name}}，数量：{{items2_quantity}}，单价：{{items2_price}}
3. 商品名称：{{items3_name}}，数量：{{items3_quantity}}，单价：{{items3_price}}
                </div>

                <h3>4.5 最佳实践</h3>
                <ul>
                    <li><strong>推荐使用方法1（固定数量）</strong>：最稳定，易于控制格式</li>
                    <li><strong>方法2适合动态数据</strong>：当您有结构化的列表数据时</li>
                    <li><strong>在模板中预留足够的行</strong>：确保有足够的空间显示所有列表项</li>
                    <li><strong>使用一致的命名规则</strong>：如 items1_name, items2_name 等</li>
                </ul>
            </section>

            <section id="api" class="section">
                <h2>5. API使用说明</h2>

                <h3>5.1 打印请求API</h3>
                <p><strong>端点</strong>: <code>/api/print</code></p>
                <p><strong>方法</strong>: POST</p>
                <p><strong>请求格式</strong>:</p>
                <div class="code-block">
{
    "template_name": "模板名称",
    "data": {
        "key1": "value1",
        "key2": "value2",
        ...
    }
}
                </div>

                <p><strong>参数说明</strong>:</p>
                <ul>
                    <li><code>template_name</code>: 模板文件名（不含扩展名，系统会自动查找.xlsx或.docx文件）</li>
                    <li><code>data</code>: 包含要填充到模板中的数据，键名应与模板中的占位符名称一致</li>
                </ul>

                <p><strong>响应格式</strong>:</p>
                <div class="code-block">
{
    "success": true,
    "message": "Print request processed successfully",
    "display_url": "/display"
}
                </div>

                <h3>5.2 示例请求</h3>
                <p>使用curl发送请求：</p>
                <div class="code-block">
curl -X POST -H "Content-Type: application/json" -d '{
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-01",
        "company": "Test Company",
        "contact": "John Doe",
        "phone": "13800138000",
        "item1_name": "Product 1",
        "item1_quantity": "10",
        "item1_price": "100",
        "item2_name": "Product 2",
        "item2_quantity": "5",
        "item2_price": "200",
        "total_amount": "2000",
        "remarks": "Test remarks"
    }
}' http://localhost:5000/api/print
                </div>

                <p>使用Python发送请求：</p>
                <div class="code-block">
import requests

url = "http://localhost:5000/api/print"
data = {
    "template_name": "dayinceshi",
    "data": {
        "date": "2023-05-01",
        "company": "Test Company",
        "contact": "John Doe",
        "phone": "13800138000",
        "item1_name": "Product 1",
        "item1_quantity": "10",
        "item1_price": "100",
        "item2_name": "Product 2",
        "item2_quantity": "5",
        "item2_price": "200",
        "total_amount": "2000",
        "remarks": "Test remarks"
    }
}

response = requests.post(url, json=data)
print(response.json())
                </div>
            </section>

            <section id="view-print" class="section">
                <h2>6. 查看和打印</h2>

                <h3>6.1 访问显示页面</h3>
                <p>发送打印请求后，系统会处理模板并生成HTML页面。您可以通过访问以下URL查看打印结果：</p>
                <p><a href="/display">/display</a></p>

                <h3>6.2 打印页面</h3>
                <p>在显示页面上，您可以：</p>
                <ul>
                    <li>查看填充后的数据</li>
                    <li>点击"打印"按钮直接打印页面</li>
                    <li>点击"返回首页"返回系统首页</li>
                </ul>

                <h3>6.3 打印特性</h3>
                <p>本系统的打印页面具有以下特性：</p>
                <ul>
                    <li><strong>纯白背景</strong>：打印时使用纯白背景，无阴影框</li>
                    <li><strong>隐藏非打印元素</strong>：打印时自动隐藏页面头部、页脚和按钮等非打印元素</li>
                    <li><strong>适合打印的布局</strong>：打印时自动调整布局，使其更适合打印</li>
                </ul>
            </section>

            <section id="nocobase" class="section">
                <h2>7. 与nocobase集成</h2>

                <h3>7.1 配置nocobase</h3>
                <p>在nocobase中，您可以通过以下步骤配置与打印系统的集成：</p>
                <ol>
                    <li>创建一个动作按钮</li>
                    <li>配置HTTP请求动作</li>
                    <li>设置请求URL为打印系统的API端点</li>
                    <li>配置请求方法为POST</li>
                    <li>设置请求体格式为JSON</li>
                    <li>配置请求数据映射，将nocobase中的字段映射到打印模板中的占位符</li>
                </ol>

                <h3>7.2 示例nocobase配置</h3>
                <div class="code-block">
{
  "type": "request",
  "method": "post",
  "url": "http://your-print-server:5000/api/print",
  "headers": {
    "Content-Type": "application/json"
  },
  "data": {
    "template_name": "dayinceshi",
    "data": {
      "date": "{% raw %}{{$date}}{% endraw %}",
      "company": "{% raw %}{{$company}}{% endraw %}",
      "contact": "{% raw %}{{$contact}}{% endraw %}",
      "phone": "{% raw %}{{$phone}}{% endraw %}",
      "item1_name": "{% raw %}{{$items[0].name}}{% endraw %}",
      "item1_quantity": "{% raw %}{{$items[0].quantity}}{% endraw %}",
      "item1_price": "{% raw %}{{$items[0].price}}{% endraw %}",
      "item2_name": "{% raw %}{{$items[1].name}}{% endraw %}",
      "item2_quantity": "{% raw %}{{$items[1].quantity}}{% endraw %}",
      "item2_price": "{% raw %}{{$items[1].price}}{% endraw %}",
      "total_amount": "{% raw %}{{$total_amount}}{% endraw %}",
      "remarks": "{% raw %}{{$remarks}}{% endraw %}"
    }
  },
  "successMessage": "打印请求已发送"
}
                </div>
            </section>

            <section id="faq" class="section">
                <h2>8. 常见问题</h2>

                <h3>8.1 模板未找到</h3>
                <p><strong>问题</strong>: 系统返回"Template 'xxx' not found"错误</p>
                <p><strong>解决方案</strong>:</p>
                <ul>
                    <li>确认模板文件已放置在<code>templates</code>目录下</li>
                    <li>确认模板名称拼写正确（不含扩展名）</li>
                    <li>确认模板文件格式为.xlsx或.docx</li>
                </ul>

                <h3>8.2 数据未正确填充</h3>
                <p><strong>问题</strong>: 模板中的占位符未被替换</p>
                <p><strong>解决方案</strong>:</p>
                <ul>
                    <li>确认占位符格式为<code>{{变量名}}</code></li>
                    <li>确认请求中的数据键名与模板中的占位符名称一致</li>
                    <li>检查日志文件查看详细错误信息</li>
                </ul>

                <h3>8.3 列表数据处理问题</h3>
                <p><strong>问题</strong>: 列表数据未正确显示</p>
                <p><strong>解决方案</strong>:</p>
                <ul>
                    <li>使用固定数量的列表项方法（推荐）</li>
                    <li>确保列表数据结构正确</li>
                    <li>检查占位符命名规则（如items1_name, items2_name）</li>
                    <li>验证JSON数据格式是否正确</li>
                </ul>

                <h3>8.4 打印时仍有阴影或背景色</h3>
                <p><strong>问题</strong>: 打印预览或实际打印时仍显示阴影或背景色</p>
                <p><strong>解决方案</strong>:</p>
                <ul>
                    <li>在浏览器的打印设置中勾选"背景图形"选项</li>
                    <li>尝试使用不同的浏览器进行打印</li>
                </ul>
            </section>
        </main>
        <footer>
            <p>&copy; 2023 模板打印系统</p>
        </footer>
    </div>
</body>
</html>
