/* 基本样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #333;
    font-size: 24px;
}

.actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
button, .button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover, .button:hover {
    background-color: #45a049;
}

/* 主要内容区域 */
main {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.info-section, .api-section {
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

h3 {
    color: #444;
    margin: 15px 0 10px;
}

p {
    margin-bottom: 15px;
}

ol, ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

li {
    margin-bottom: 5px;
}

pre {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin-bottom: 15px;
    border: 1px solid #ddd;
}

code {
    background-color: #f8f8f8;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th, .table td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.table th {
    background-color: #f8f8f8;
    font-weight: bold;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-bordered {
    border: 1px solid #ddd;
}

/* 合并单元格样式 */
.table td[colspan], .table th[colspan] {
    text-align: center;
}

.table td[rowspan], .table th[rowspan] {
    vertical-align: middle;
}

/* Word内容样式 */
.word-content p {
    margin-bottom: 10px;
}

/* 时间戳 */
.timestamp {
    color: #777;
    font-size: 14px;
    margin-bottom: 20px;
    text-align: right;
}

/* 页脚样式 */
footer {
    text-align: center;
    padding: 20px;
    margin-top: 20px;
    color: #777;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        align-items: flex-start;
    }

    .actions {
        margin-top: 10px;
    }
}

/* 打印样式 */
@media print {
    /* 强制设置页面背景为白色 */
    @page {
        margin: 0.5in;
        background-color: white;
        color: black;
    }

    html {
        background-color: white !important;
        color: black !important;
    }

    body {
        background-color: white !important;
        color: black !important;
        margin: 0 !important;
        padding: 0 !important;
        font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    }

    .container {
        background-color: white !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        max-width: none !important;
    }

    main {
        background-color: white !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        border-radius: 0 !important;
    }

    .print-content, .content {
        background-color: white !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }

    .table {
        width: 100% !important;
        border-collapse: collapse !important;
        background-color: white !important;
        margin: 0 !important;
        page-break-inside: auto;
    }

    .table th, .table td {
        border: 1px solid #000 !important;
        background-color: white !important;
        padding: 6px 8px !important;
        vertical-align: middle !important;
        color: black !important;
        page-break-inside: avoid;
    }

    /* 保持表格样式但确保背景为白色 */
    .table th[style*="background-color"] {
        background-color: #f0f0f0 !important;
        color: black !important;
    }

    .table td[style*="background-color"]:not([style*="background-color: white"]) {
        opacity: 0.3 !important;
    }

    /* 打印时的合并单元格样式 */
    .table td[colspan], .table th[colspan] {
        text-align: center !important;
        background-color: white !important;
    }

    .table td[rowspan], .table th[rowspan] {
        vertical-align: middle !important;
        background-color: white !important;
    }

    /* 移除条纹背景 */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: white !important;
    }

    .table-striped tbody tr:nth-of-type(even) {
        background-color: white !important;
    }

    /* 隐藏不需要打印的元素 */
    .no-print, .actions, button, .button, footer, header {
        display: none !important;
    }

    /* 移除所有阴影和边框效果 */
    * {
        box-shadow: none !important;
        text-shadow: none !important;
        background-image: none !important;
    }

    /* 确保所有文本为黑色 */
    h1, h2, h3, h4, h5, h6, p, span, div, td, th {
        color: black !important;
    }

    /* 特殊颜色文本在打印时保持可读性 */
    [style*="color: #"] {
        color: black !important;
    }
}
