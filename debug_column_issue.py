import openpyxl
import re

# 检查模板的实际结构
wb = openpyxl.load_workbook('templates/delivery.xlsx')
ws = wb.active

print("=== 检查模板第4行（表头）和第5行（数据行） ===")

# 检查第4行（表头）
print("第4行（表头）:")
for col in range(1, 10):
    cell = ws.cell(row=4, column=col)
    value = cell.value if cell.value else ''
    print(f"  第{col}列: '{value}'")

# 检查第5行（模板行）
print("\n第5行（模板行）:")
for col in range(1, 10):
    cell = ws.cell(row=5, column=col)
    value = cell.value if cell.value else ''
    print(f"  第{col}列: '{value}'")

print("\n=== 分析列对应关系 ===")
headers = {
    1: "序号",
    2: "批号", 
    3: "名称",
    4: "材质",
    5: "单位",
    6: "数量",
    7: "单价", 
    8: "金额",
    9: "备注"
}

placeholders = {
    2: "{{list.batno}}",  # 批号
    3: "{{list.name}}",   # 名称
    4: "{{list.matrial}}", # 材质
    5: "{{list.unit}}"    # 单位
}

print("期望的列对应关系:")
for col, placeholder in placeholders.items():
    header = headers.get(col, "未知")
    print(f"  第{col}列 ({header}): {placeholder}")

print("\n实际模板中的占位符位置:")
for col in range(1, 10):
    cell = ws.cell(row=5, column=col)
    if cell.value and isinstance(cell.value, str) and '{{list.' in cell.value:
        header = headers.get(col, "未知")
        print(f"  第{col}列 ({header}): {cell.value}")

print("\n=== 检查数据填充逻辑 ===")
# 模拟数据填充过程
test_data = {
    "batno": "TEST001",
    "name": "测试商品",
    "matrial": "测试材料", 
    "unit": "件"
}

# 扫描模板行，查找列占位符
column_templates = {}
template_row = 5

for col_idx in range(1, ws.max_column + 1):
    template_cell = ws.cell(row=template_row, column=col_idx)
    if template_cell.value and isinstance(template_cell.value, str):
        # 查找形如 {{list.field}} 的占位符
        list_matches = re.findall(r'\{\{list\.(\w+)\}\}', template_cell.value)
        
        if list_matches:
            column_templates[col_idx] = {
                'field': list_matches[0],
                'template': template_cell.value
            }

print("检测到的列模板:")
for col_idx, template_info in column_templates.items():
    field = template_info['field']
    value = test_data.get(field, '(未找到)')
    header = headers.get(col_idx, "未知")
    print(f"  第{col_idx}列 ({header}): {field} -> '{value}'")

print("\n=== 问题诊断 ===")
# 检查是否有列偏移问题
expected_positions = {
    'batno': 2,    # 应该在第2列（批号）
    'name': 3,     # 应该在第3列（名称）
    'matrial': 4,  # 应该在第4列（材质）
    'unit': 5      # 应该在第5列（单位）
}

actual_positions = {info['field']: col for col, info in column_templates.items()}

print("字段位置对比:")
for field, expected_col in expected_positions.items():
    actual_col = actual_positions.get(field, '未找到')
    expected_header = headers.get(expected_col, "未知")
    actual_header = headers.get(actual_col, "未知") if actual_col != '未找到' else "未知"
    
    if actual_col == expected_col:
        print(f"  ✅ {field}: 期望第{expected_col}列({expected_header}), 实际第{actual_col}列({actual_header})")
    else:
        print(f"  ❌ {field}: 期望第{expected_col}列({expected_header}), 实际第{actual_col}列({actual_header})")

if actual_positions == expected_positions:
    print("\n✅ 列位置映射正确")
else:
    print("\n❌ 列位置映射错误，这就是数据错位的原因！")
    
    # 计算偏移量
    offsets = []
    for field in expected_positions:
        if field in actual_positions:
            offset = actual_positions[field] - expected_positions[field]
            offsets.append(offset)
    
    if offsets and all(o == offsets[0] for o in offsets):
        print(f"所有字段都向{'右' if offsets[0] > 0 else '左'}偏移了{abs(offsets[0])}列")
    else:
        print("偏移量不一致，需要逐个检查")
