import requests

# 简单的API测试
data = {
    "data": {
        "cdkh": "SIMPLE_TEST_001"
    },
    "template_name": "委外加工过程不良品记录表"
}

print("📄 简单API测试")
print("=" * 40)

try:
    # 发送请求
    response = requests.post("http://localhost:5000/api/print", json=data, timeout=30)
    print(f"API状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API调用成功")
        result = response.json()
        print(f"响应: {result}")
        
        print("\n🔗 查看链接:")
        print("   📄 普通页面: http://localhost:5000/display")
        print("   🖨️ 打印页面: http://localhost:5000/print")
        
    else:
        print(f"❌ API调用失败")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"错误内容: '{response.text}'")
        
except Exception as e:
    print(f"❌ 请求异常: {e}")

print("\n" + "=" * 40)
print("📄 简单API测试完成")
