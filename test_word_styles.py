import sys
import os
sys.path.append('.')

from docx import Document
from app import convert_word_to_html, extract_paragraph_style, extract_cell_style

print("🎨 Word样式提取测试")
print("=" * 60)

try:
    # 加载Word文档
    template_path = "templates/委外加工过程不良品记录表.docx"
    doc = Document(template_path)
    
    print("1. 分析Word文档样式:")
    print(f"   模板: {template_path}")
    
    # 分析段落样式
    print("\n2. 段落样式分析:")
    for i, para in enumerate(doc.paragraphs):
        if para.text.strip():
            text = para.text.strip()[:30] + "..." if len(para.text.strip()) > 30 else para.text.strip()
            
            # 提取段落样式
            para_style = extract_paragraph_style(para)
            print(f"   段落{i+1}: {text}")
            print(f"     样式: {para_style}")
            
            # 分析对齐方式
            if para.alignment is not None:
                from docx.enum.text import WD_ALIGN_PARAGRAPH
                align_map = {
                    WD_ALIGN_PARAGRAPH.LEFT: "左对齐",
                    WD_ALIGN_PARAGRAPH.CENTER: "居中",
                    WD_ALIGN_PARAGRAPH.RIGHT: "右对齐",
                    WD_ALIGN_PARAGRAPH.JUSTIFY: "两端对齐"
                }
                align_text = align_map.get(para.alignment, "未知对齐")
                print(f"     对齐: {align_text}")
            else:
                print(f"     对齐: 默认（左对齐）")
            
            # 分析字体信息
            if para.runs:
                first_run = para.runs[0]
                font_info = []
                if first_run.font.name:
                    font_info.append(f"字体: {first_run.font.name}")
                if first_run.font.size:
                    font_info.append(f"大小: {first_run.font.size.pt}pt")
                if first_run.font.bold:
                    font_info.append("粗体")
                if first_run.font.italic:
                    font_info.append("斜体")
                
                if font_info:
                    print(f"     字体: {', '.join(font_info)}")
            print()
    
    # 分析表格样式
    print("3. 表格样式分析:")
    for table_idx, table in enumerate(doc.tables):
        print(f"   表格{table_idx+1}: {len(table.rows)}行 x {len(table.columns)}列")
        
        # 分析第一行的样式（通常是表头）
        if table.rows:
            first_row = table.rows[0]
            print(f"     第1行样式:")
            for cell_idx, cell in enumerate(first_row.cells):
                if cell.text.strip():
                    cell_style = extract_cell_style(cell)
                    cell_text = cell.text.strip()[:15] + "..." if len(cell.text.strip()) > 15 else cell.text.strip()
                    print(f"       单元格{cell_idx+1} ({cell_text}): {cell_style}")
        print()
    
    # 生成带样式的HTML
    print("4. 生成带样式的HTML:")
    html_content = convert_word_to_html(doc)
    
    # 保存HTML文件
    html_file = "word_styles_test.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Word样式测试</title>
    <style>
        body {{ 
            margin: 20px; 
            background-color: white; 
        }}
        .header {{
            background-color: #f0f8ff;
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
            border-radius: 5px;
        }}
        .comparison {{
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }}
        .comparison > div {{
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }}
        .original {{
            background-color: #fff8dc;
        }}
        .styled {{
            background-color: #f0fff0;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Word样式提取测试结果</h1>
        <p><strong>测试目标:</strong> 验证字体、字体大小、居中等样式是否正确提取</p>
        <p><strong>Word模板:</strong> 委外加工过程不良品记录表.docx</p>
    </div>
    
    <div class="comparison">
        <div class="original">
            <h3>原始HTML（无样式）</h3>
            <p>之前的HTML转换没有保留Word的样式信息，所有文本都使用默认样式。</p>
        </div>
        <div class="styled">
            <h3>增强HTML（带样式）</h3>
            <p>现在的HTML转换保留了Word的字体、大小、对齐等样式信息。</p>
        </div>
    </div>
    
    <h2>📄 渲染结果（带样式）</h2>
    {html_content}
    
    <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border: 1px dashed #999;">
        <h3>✅ 样式功能验证</h3>
        <ul>
            <li><strong>字体名称:</strong> 保留Word中设置的字体（如SimSun、宋体等）</li>
            <li><strong>字体大小:</strong> 保留Word中的字体大小（pt单位）</li>
            <li><strong>文本对齐:</strong> 保留左对齐、居中、右对齐、两端对齐</li>
            <li><strong>字体样式:</strong> 保留粗体、斜体、下划线等</li>
            <li><strong>字体颜色:</strong> 保留文字颜色设置</li>
            <li><strong>表格样式:</strong> 保留单元格的字体和对齐设置</li>
        </ul>
    </div>
</body>
</html>
        """)
    
    print(f"   ✅ HTML文件已保存: {html_file}")
    print(f"   📊 HTML长度: {len(html_content)} 字符")
    
    # 检查样式关键词
    style_checks = [
        ("字体设置", "font-family:" in html_content),
        ("字体大小", "font-size:" in html_content),
        ("文本对齐", "text-align:" in html_content),
        ("边框样式", "border:" in html_content),
        ("内边距", "padding:" in html_content)
    ]
    
    print(f"\n5. 样式检查:")
    for check_name, check_result in style_checks:
        status = "✅" if check_result else "❌"
        print(f"   {status} {check_name}")
    
    print(f"\n6. HTML预览（前500字符）:")
    print("   " + "="*60)
    print(f"   {html_content[:500]}...")
    print("   " + "="*60)

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🎨 Word样式提取测试完成")

print("\n📋 功能说明:")
print("✅ 段落样式: 字体、大小、对齐方式")
print("✅ 表格样式: 单元格字体、对齐、边框")
print("✅ 文本样式: 粗体、斜体、下划线、颜色")
print("✅ 布局保持: 保持Word原始布局结构")

print("\n🔗 查看结果:")
print(f"   📁 本地文件: {os.path.abspath('word_styles_test.html')}")
print("   🌐 在线查看: http://localhost:5000/display")
print("   🖨️ 打印页面: http://localhost:5000/print")
