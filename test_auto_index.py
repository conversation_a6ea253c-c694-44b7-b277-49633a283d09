import requests

# 测试自增序号功能
data = {
    "data": {
        "list": [
            {"batno": "ITEM001", "name": "第一个商品", "matrial": "塑料", "unit": "个"},
            {"batno": "ITEM002", "name": "第二个商品", "matrial": "金属", "unit": "套"},
            {"batno": "ITEM003", "name": "第三个商品", "matrial": "木材", "unit": "件"},
            {"batno": "ITEM004", "name": "第四个商品", "matrial": "玻璃", "unit": "块"}
        ],
        "name": "自增序号测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

print("=== 测试自增序号功能 ===")
response = requests.post("http://localhost:5000/api/print", json=data)
print(f"状态码: {response.status_code}")

if response.status_code == 200:
    print("✅ API调用成功")
    print("请检查浏览器中的显示效果：")
    print("1. 第1列应该显示序号：1, 2, 3, 4")
    print("2. 序号应该从1开始递增")
    print("3. 序号应该与数据行对应")
    print()
    print("🔗 查看链接:")
    print("   普通页面: http://localhost:5000/display")
    print("   打印页面: http://localhost:5000/print")
else:
    print(f"❌ API调用失败: {response.text}")

print("\n=== 测试完成 ===")
print("预期结果:")
print("序号列 | 批号列    | 名称列      | 材质列 | 单位列")
print("  1    | ITEM001  | 第一个商品   | 塑料   | 个")
print("  2    | ITEM002  | 第二个商品   | 金属   | 套")
print("  3    | ITEM003  | 第三个商品   | 木材   | 件")
print("  4    | ITEM004  | 第四个商品   | 玻璃   | 块")
