# 自增序号功能说明

## 🎯 功能概述

模板打印系统现在支持**自动生成递增序号**功能，为动态列表中的每一行自动添加从1开始的序号。

## ✨ 功能特点

- **自动检测**：系统自动检测第1列是否为空的序号列
- **自动递增**：序号从1开始，自动递增（1, 2, 3, 4...）
- **样式保持**：序号继承模板行的字体、对齐等样式
- **无需修改数据**：不需要在JSON数据中添加序号字段
- **完全自动**：无需手动配置，系统自动处理

## 🔧 工作原理

### 自动检测条件
系统会自动检测模板中的第1列：
- 如果第1列为空或只包含空白字符
- 系统自动将其识别为序号列
- 自动为每行数据生成递增序号

### 序号生成规则
- **起始值**：从1开始
- **递增规则**：每行递增1（1, 2, 3, 4...）
- **数据类型**：整数
- **样式继承**：继承模板行第1列的所有样式

## 📋 使用示例

### 模板结构
```
| 序号 | 批号 | 名称 | 材质 | 单位 |
|------|------|------|------|------|
| (空) | {{list.batno}} | {{list.name}} | {{list.matrial}} | {{list.unit}} |
```

### JSON数据
```json
{
  "template_name": "delivery",
  "data": {
    "list": [
      {"batno": "ITEM001", "name": "商品1", "matrial": "塑料", "unit": "个"},
      {"batno": "ITEM002", "name": "商品2", "matrial": "金属", "unit": "套"},
      {"batno": "ITEM003", "name": "商品3", "matrial": "木材", "unit": "件"}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
```

### 生成结果
```
| 序号 | 批号    | 名称   | 材质 | 单位 |
|------|---------|--------|------|------|
|  1   | ITEM001 | 商品1  | 塑料 | 个   |
|  2   | ITEM002 | 商品2  | 金属 | 套   |
|  3   | ITEM003 | 商品3  | 木材 | 件   |
```

## 🎨 样式说明

### 样式继承
- 序号会继承模板行第1列的所有样式：
  - 字体名称和大小
  - 字体颜色（黑色/红色等）
  - 对齐方式（居中/左对齐等）
  - 边框样式
  - 背景颜色

### 显示效果
- **Excel文件**：序号正确显示在第1列
- **HTML显示**：序号在网页中正确显示
- **打印效果**：序号在打印时清晰可见

## 🔄 兼容性

### 向后兼容
- 现有模板无需修改即可使用
- 现有数据格式完全兼容
- 不影响其他功能的正常使用

### 模板要求
- 第1列必须为空（用作序号列）
- 第1列应该有"序号"表头
- 建议第1列设置合适的列宽

## 📝 注意事项

1. **第1列检测**：只有第1列为空时才会自动添加序号
2. **数据完整性**：序号不会影响原始数据的完整性
3. **样式一致性**：序号样式与模板行保持一致
4. **打印友好**：序号在打印时清晰可见

## 🚀 使用建议

### 模板设计
- 将第1列设计为序号列
- 设置合适的列宽（建议30-50像素）
- 使用居中对齐以获得最佳视觉效果

### 数据准备
- 无需在数据中添加序号字段
- 保持现有的数据结构不变
- 系统会自动处理序号生成

## ✅ 验证方法

### 检查Excel文件
```python
# 检查生成的Excel文件
wb = openpyxl.load_workbook('filled_outputs/filled_xxx.xlsx')
ws = wb.active

# 验证序号列
for row in range(5, 9):  # 数据行
    index_value = ws.cell(row=row, column=1).value
    print(f"第{row}行序号: {index_value}")
```

### 检查HTML显示
- 访问 `http://localhost:5000/display` 查看普通页面
- 访问 `http://localhost:5000/print` 查看打印页面
- 确认第1列显示正确的序号

## 🎊 总结

自增序号功能为模板打印系统增加了重要的实用功能：

- **✅ 自动化**：无需手动添加序号
- **✅ 智能化**：自动检测和处理
- **✅ 美观性**：序号样式与模板一致
- **✅ 实用性**：提高文档的可读性
- **✅ 兼容性**：不影响现有功能

现在您的送货单等文档会自动包含清晰的序号，使文档更加专业和易读！
