# {{sum.field}} 占位符完整指南

## 🎯 功能概述

`{{sum.field}}` 占位符是一个革命性的功能，专门解决**动态长度列表的求和问题**。与传统的Excel SUM公式不同，这个占位符能够自动适应list长度的变化，无需手动设置范围。

## 🆚 两种SUM实现方式对比

### 方式1：Excel SUM公式（传统方式）
```excel
=SUM(F5:F5)  # 需要手动设置范围，系统自动调整
```
**优点**：
- ✅ Excel原生支持
- ✅ 支持复杂计算
- ✅ 可以在Excel中直接编辑

**缺点**：
- ❌ 需要预设公式
- ❌ 范围固定，需要系统调整
- ❌ 设置复杂

### 方式2：{{sum.field}} 占位符（推荐）
```
{{sum.quantity}}  # 自动计算所有quantity字段的总和
```
**优点**：
- ✅ 完全动态，自动适应list长度
- ✅ 简单易用，无需设置范围
- ✅ 智能计算，自动处理数据类型
- ✅ 错误容忍，跳过无效数据
- ✅ 灵活位置，可放在任意位置

**缺点**：
- ❌ 只支持简单求和
- ❌ 不支持复杂公式

## 🔧 使用方法

### 1. 模板设计

在Excel模板中使用 `{{sum.field}}` 占位符：

```excel
| 序号 | 批号 | 名称 | 数量 | 单价 | 金额 |
|------|------|------|------|------|------|
| {{auto_index}} | {{list.batno}} | {{list.name}} | {{list.quantity}} | {{list.price}} | {{list.amount}} |
| 合计：                    | {{sum.quantity}} |      | {{sum.amount}} |
```

### 2. JSON数据格式

```json
{
  "template_name": "your_template",
  "data": {
    "list": [
      {"batno": "A001", "name": "产品A", "quantity": 10, "price": 100, "amount": 1000},
      {"batno": "B002", "name": "产品B", "quantity": 20, "price": 200, "amount": 4000}
    ],
    "name": "客户名称",
    "creator": "创建者"
  }
}
```

### 3. 生成结果

```
| 序号 | 批号 | 名称   | 数量 | 单价 | 金额 |
|------|------|--------|------|------|------|
|  1   | A001 | 产品A  | 10   | 100  | 1000 |
|  2   | B002 | 产品B  | 20   | 200  | 4000 |
| 合计：              | 30   |      | 5000 |
```

## 📋 支持的数据类型

### 数字类型
```json
{"quantity": 10}        # 整数
{"amount": 1000.50}     # 浮点数
```

### 字符串数字
```json
{"quantity": "10"}      # 字符串数字
{"amount": "1,000.50"}  # 带逗号的数字
```

### 错误处理
```json
{"quantity": null}      # null值 -> 跳过
{"quantity": ""}        # 空字符串 -> 跳过
{"quantity": "abc"}     # 非数字 -> 跳过
```

## 🎨 高级用法

### 多个SUM占位符
```excel
| 合计： | {{sum.quantity}} | {{sum.weight}} | {{sum.amount}} |
```

### 文本嵌入
```excel
| 总数量：{{sum.quantity}}个 | 总金额：{{sum.amount}}元 |
```

### 不同位置使用
```excel
| 页眉：总计{{sum.amount}}元 |
| ... 数据行 ... |
| 页脚：共{{sum.quantity}}件商品 |
```

## ⚠️ 重要注意事项

### 字段名匹配
**必须确保字段名完全匹配**：
```
模板中：{{sum.quantity}}
数据中：{"quantity": 10}  ✅ 正确

模板中：{{sum.quantity}}
数据中：{"qty": 10}       ❌ 错误，字段名不匹配
```

### 位置要求
- `{{sum.field}}` 占位符通常放在合计行
- 在数据行中，SUM占位符会显示为空
- 可以在模板的任意位置使用

### 数据要求
- 字段必须包含数值数据
- 支持整数、浮点数和数字字符串
- 自动跳过null、空字符串和非数字值

## 🚀 实际应用场景

### 1. 送货单
```excel
| 序号 | 商品 | 数量 | 金额 |
|------|------|------|------|
| {{auto_index}} | {{list.name}} | {{list.qty}} | {{list.amount}} |
| 合计：        | {{sum.qty}} | {{sum.amount}} |
```

### 2. 采购清单
```excel
| 项目 | 单价 | 数量 | 小计 |
|------|------|------|------|
| {{list.item}} | {{list.price}} | {{list.qty}} | {{list.total}} |
| 总计：                | {{sum.qty}} | {{sum.total}} |
```

### 3. 财务报表
```excel
| 部门 | 收入 | 支出 | 利润 |
|------|------|------|------|
| {{list.dept}} | {{list.income}} | {{list.expense}} | {{list.profit}} |
| 汇总：        | {{sum.income}} | {{sum.expense}} | {{sum.profit}} |
```

## 🔧 技术实现

### 工作原理
1. **检测阶段**：扫描模板，识别 `{{sum.field}}` 占位符
2. **计算阶段**：遍历list数据，计算指定字段的总和
3. **替换阶段**：将计算结果替换占位符
4. **渲染阶段**：在合计行显示结果，数据行显示为空

### 计算逻辑
```python
def calculate_sum_for_field(list_data, field_name):
    total = 0
    for item in list_data:
        if field_name in item:
            value = item[field_name]
            if isinstance(value, (int, float)):
                total += value
            elif isinstance(value, str):
                try:
                    total += float(value.replace(',', ''))
                except ValueError:
                    continue  # 跳过无效数据
    return total
```

## 💡 最佳实践

### 模板设计
1. **清晰命名**：使用有意义的字段名
2. **合理位置**：将SUM占位符放在合计行
3. **样式统一**：保持与数据行相同的样式

### 数据准备
1. **字段匹配**：确保数据字段名与模板一致
2. **数据类型**：使用数字类型或数字字符串
3. **数据完整性**：处理null值和异常数据

### 错误预防
1. **字段验证**：检查字段名是否匹配
2. **数据验证**：确保数值字段包含有效数据
3. **测试验证**：使用小数据集测试功能

## 🎊 总结

`{{sum.field}}` 占位符是模板打印系统的一个重要创新：

### 核心价值
- **🎯 解决痛点**：完美解决动态长度列表的求和问题
- **🔧 简化操作**：无需复杂的公式设置
- **📊 智能计算**：自动处理各种数据类型
- **🛡️ 错误容忍**：优雅处理异常数据

### 适用场景
- **📋 动态列表**：长度不固定的数据列表
- **📊 简单统计**：基础的数值求和需求
- **🚀 快速开发**：需要快速实现求和功能
- **💼 业务报表**：各种业务单据和报表

**现在您可以轻松处理任意长度的动态列表求和，让模板更加智能和灵活！** 🎉

## 🔗 相关功能

- **自增序号**：`{{auto_index}}` 占位符
- **动态列表**：`{{list.field}}` 占位符  
- **Excel公式**：`=SUM()` 公式自动更新
- **样式保持**：完整的Excel样式支持
