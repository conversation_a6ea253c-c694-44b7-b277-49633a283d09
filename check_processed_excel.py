import openpyxl
import requests
import json
import time

# 1. 发送API请求
print("=== 发送API请求 ===")
data = {
    "data": {
        "list": [
            {
                "batno": "TEST001",
                "name": "测试商品1",
                "matrial": "塑料",
                "unit": "个"
            }
        ],
        "name": "测试客户",
        "creator": "测试员"
    },
    "template_name": "delivery"
}

response = requests.post("http://localhost:5000/api/print", json=data)
print(f"API响应: {response.status_code} - {response.text}")

# 等待一下确保处理完成
time.sleep(1)

# 2. 检查是否有临时文件生成
import os
print("\n=== 检查临时文件 ===")
temp_files = [f for f in os.listdir('.') if f.startswith('temp_') and f.endswith('.xlsx')]
print(f"找到临时文件: {temp_files}")

if temp_files:
    # 检查最新的临时文件
    latest_temp = max(temp_files, key=lambda x: os.path.getctime(x))
    print(f"检查最新临时文件: {latest_temp}")
    
    wb = openpyxl.load_workbook(latest_temp)
    ws = wb.active
    
    print("处理后的Excel内容:")
    for row in range(1, min(10, ws.max_row + 1)):
        row_data = []
        for col in range(1, min(8, ws.max_column + 1)):
            cell = ws.cell(row=row, column=col)
            value = cell.value if cell.value else ''
            row_data.append(str(value)[:20])
        print(f'第{row}行: {row_data}')
    
    # 检查第5行是否包含数据
    if ws.cell(row=5, column=2).value == "TEST001":
        print("✅ 数据已正确填充到Excel")
    else:
        print(f"❌ 第5行第2列的值是: {ws.cell(row=5, column=2).value}")
else:
    print("❌ 没有找到临时文件")

# 3. 检查显示页面
print("\n=== 检查显示页面 ===")
display_response = requests.get("http://localhost:5000/display")
if "TEST001" in display_response.text:
    print("✅ 显示页面包含数据")
else:
    print("❌ 显示页面不包含数据")
    if "{{list.batno}}" in display_response.text:
        print("❌ 显示页面仍包含未替换的占位符")

print("\n=== 检查完成 ===")
