import requests
import json

# API端点
url = "http://localhost:5000/api/print"

# 使用您提供的真实数据格式
print("=== 测试发货单模板（真实数据格式）===")

# 您提供的数据结构
data = {
    "template_name": "delivery",
    "data": {
        "list": [
            {
                "createdAt": "2025-05-28T07:32:46.190Z",
                "updatedAt": "2025-05-28T09:44:59.152Z",
                "id": 1,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a",
                "name": "sdad",
                "matrial": "wd",
                "unit": "个",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.156Z",
                "updatedAt": "2025-05-28T09:44:59.156Z",
                "id": 2,
                "createdById": 1,
                "updatedById": 1,
                "batno": "ds",
                "name": "da",
                "matrial": None,
                "unit": "发a",
                "quantity": 2,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.158Z",
                "updatedAt": "2025-05-28T09:44:59.158Z",
                "id": 3,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a das",
                "name": "阿萨 ",
                "matrial": "阿发",
                "unit": "阿发",
                "quantity": 3,
                "note": None,
                "amount": None,
                "delivery_f": 1
            },
            {
                "createdAt": "2025-05-28T09:44:59.160Z",
                "updatedAt": "2025-05-28T09:44:59.160Z",
                "id": 4,
                "createdById": 1,
                "updatedById": 1,
                "batno": "a阿发",
                "name": "eqa'd",
                "matrial": None,
                "unit": "阿发",
                "quantity": 4,
                "note": None,
                "amount": None,
                "delivery_f": 1
            }
        ],
        "name": "测试客户132",
        "creator": "Super Admin",
        "delivery_date": "2023-12-01",
        "delivery_note": "请仔细核对商品数量和规格，如有问题请及时联系。货物送达后请在发货单上签字确认。"
    }
}

response = requests.post(url, json=data)
print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    print("\n✅ 测试成功！")
    print("请访问 http://localhost:5000/display 查看发货单")
    print("\n📋 预期效果:")
    print("1. 标题: 发货单")
    print("2. 客户名称: 测试客户132")
    print("3. 制单人: Super Admin")
    print("4. 动态列表: 4个商品项目")
    print("5. 列表下方: 发货说明和签名区域")
    print("\n🔍 检查要点:")
    print("- 列表项数量是否正确（4项）")
    print("- 每个列表项的字段是否正确显示")
    print("- None值是否正确处理为空")
    print("- 列表下方的内容是否正确显示")
else:
    print(f"\n❌ 测试失败: {response.text}")

print("\n" + "="*60)
print("数据结构说明:")
print("✅ 支持动态数量的列表项")
print("✅ 支持复杂的嵌套数据结构")
print("✅ 自动处理None值")
print("✅ 列表下方内容自动调整位置")
print("✅ 保持Excel样式和格式")
